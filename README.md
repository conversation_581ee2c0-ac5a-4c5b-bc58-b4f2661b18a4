# Game Stats Tracker

A comprehensive Flask-based web application for tracking gaming statistics across multiple games including Valorant, CS2, and BGMI. Features user authentication, social interactions, achievement system, and real-time stats synchronization.

## Features

### Core Features

- **Multi-Game Support**: Track stats for Valorant, Counter-Strike 2, and BGMI
- **User Authentication**: Secure registration, login, and profile management
- **Player Profiles**: Manage game usernames and regions for different games
- **Real-time Stats**: Automatic synchronization of match data and statistics
- **Interactive Dashboard**: Visual analytics with charts and performance metrics

### Social Features

- **Friend System**: Send/accept friend requests and manage friendships
- **Player Search**: Discover and connect with other players
- **Stats Comparison**: Compare your performance with friends
- **Leaderboards**: Global and friends-only leaderboards

### Gamification

- **Achievement System**: Earn badges and points for various milestones
- **Progress Tracking**: Monitor improvement over time
- **Performance Analytics**: Detailed breakdowns of K/D ratios, win rates, and more

### OAuth Integration

- **Steam Integration**: Link Steam account for CS2 stats
- **Riot Games Integration**: Connect Riot account for Valorant data
- **Automatic Sync**: Seamless data synchronization from linked accounts

### Technical Features

- **Background Processing**: Celery-based task queue for API data fetching
- **Caching Layer**: Redis caching for improved performance
- **Rate Limiting**: Intelligent API rate limiting and retry mechanisms
- **Responsive Design**: Mobile-friendly Bootstrap 5 interface

## Technology Stack

### Backend

- **Flask**: Web framework
- **SQLAlchemy**: Database ORM
- **PostgreSQL**: Primary database
- **Redis**: Caching and message broker
- **Celery**: Background task processing

### Frontend

- **Bootstrap 5**: CSS framework
- **Chart.js**: Data visualization
- **Font Awesome**: Icons
- **Jinja2**: Template engine

### APIs

- **Riot Games API**: Valorant statistics
- **Steam Web API**: CS2 and Steam data
- **OAuth 2.0**: Account linking

## Quick Start

### Prerequisites

- Python 3.8+
- PostgreSQL
- Redis
- API keys for Riot Games and Steam

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd game_stat_tracker
```

2. Create a virtual environment:

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:

```bash
pip install -r requirements.txt
```

4. Set up environment variables:

```bash
cp .env.example .env
# Edit .env with your configuration
```

5. Initialize the database:

```bash
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
```

6. Start Redis server:

```bash
redis-server
```

7. Start Celery worker (in a separate terminal):

```bash
celery -A app.celery worker --loglevel=info
```

8. Run the application:

```bash
python app.py
```

Visit `http://localhost:5000` to access the application.

## API Keys Setup

### Riot Games API

1. Visit [Riot Developer Portal](https://developer.riotgames.com/)
2. Create an account and generate an API key
3. Add the key to your `.env` file as `RIOT_API_KEY`

### Steam Web API

1. Visit [Steam Web API](https://steamcommunity.com/dev/apikey)
2. Generate an API key
3. Add the key to your `.env` file as `STEAM_API_KEY`

## Development

### Running Tests

```bash
pytest
```

### Code Formatting

```bash
black .
flake8 .
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
