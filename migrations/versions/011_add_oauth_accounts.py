"""Add OAuth accounts table

Revision ID: 011_add_oauth_accounts
Revises: 010_add_achievements
Create Date: 2024-01-15 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '011_add_oauth_accounts'
down_revision = '010_add_achievements'
branch_labels = None
depends_on = None


def upgrade():
    # Create oauth_accounts table
    op.create_table('oauth_accounts',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('provider', sa.String(length=50), nullable=False),
        sa.Column('provider_user_id', sa.String(length=255), nullable=False),
        sa.Column('access_token', sa.Text(), nullable=True),
        sa.Column('refresh_token', sa.Text(), nullable=True),
        sa.Column('token_expires_at', sa.DateTime(), nullable=True),
        sa.Column('user_info', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('provider', 'provider_user_id', name='unique_provider_account')
    )
    
    # Create indexes
    op.create_index('idx_user_provider', 'oauth_accounts', ['user_id', 'provider'], unique=False)


def downgrade():
    # Drop indexes
    op.drop_index('idx_user_provider', table_name='oauth_accounts')
    
    # Drop table
    op.drop_table('oauth_accounts')
