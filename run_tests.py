#!/usr/bin/env python3
"""
Test runner script for Game Stats Tracker
"""
import sys
import subprocess
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{'='*50}")
    print(f"Running: {description}")
    print(f"Command: {command}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"ERROR: {description} failed!")
        print(f"Return code: {e.returncode}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        return False

def main():
    """Main test runner function"""
    print("Game Stats Tracker - Test Runner")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("app").exists() or not Path("tests").exists():
        print("ERROR: Please run this script from the project root directory")
        sys.exit(1)
    
    # Set environment variables for testing
    os.environ['FLASK_ENV'] = 'testing'
    os.environ['TESTING'] = 'true'
    
    # Test commands to run
    test_commands = [
        {
            'command': 'python -m pytest tests/ -v',
            'description': 'Running all tests with verbose output'
        },
        {
            'command': 'python -m pytest tests/test_models.py -v',
            'description': 'Running model tests'
        },
        {
            'command': 'python -m pytest tests/test_auth.py -v',
            'description': 'Running authentication tests'
        },
        {
            'command': 'python -m pytest tests/test_services.py -v',
            'description': 'Running service tests'
        },
        {
            'command': 'python -m pytest --cov=app --cov-report=html --cov-report=term-missing',
            'description': 'Running tests with coverage report'
        }
    ]
    
    # Parse command line arguments
    if len(sys.argv) > 1:
        test_type = sys.argv[1].lower()
        
        if test_type == 'models':
            test_commands = [test_commands[1]]
        elif test_type == 'auth':
            test_commands = [test_commands[2]]
        elif test_type == 'services':
            test_commands = [test_commands[3]]
        elif test_type == 'coverage':
            test_commands = [test_commands[4]]
        elif test_type == 'quick':
            test_commands = [test_commands[0]]
        elif test_type == 'help':
            print("\nUsage: python run_tests.py [test_type]")
            print("\nAvailable test types:")
            print("  models    - Run model tests only")
            print("  auth      - Run authentication tests only")
            print("  services  - Run service tests only")
            print("  coverage  - Run tests with coverage report")
            print("  quick     - Run all tests (default)")
            print("  help      - Show this help message")
            return
    
    # Run the tests
    success_count = 0
    total_count = len(test_commands)
    
    for test_cmd in test_commands:
        if run_command(test_cmd['command'], test_cmd['description']):
            success_count += 1
        else:
            print(f"\nFailed to run: {test_cmd['description']}")
    
    # Summary
    print(f"\n{'='*50}")
    print(f"TEST SUMMARY")
    print(f"{'='*50}")
    print(f"Successful: {success_count}/{total_count}")
    print(f"Failed: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 All tests completed successfully!")
        sys.exit(0)
    else:
        print("❌ Some tests failed. Please check the output above.")
        sys.exit(1)

if __name__ == '__main__':
    main()
