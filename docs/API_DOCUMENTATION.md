# Game Stats Tracker - API Documentation

This document provides comprehensive documentation for the Game Stats Tracker API endpoints and services.

## Table of Contents

1. [Authentication Endpoints](#authentication-endpoints)
2. [Player Profile Endpoints](#player-profile-endpoints)
3. [Stats Endpoints](#stats-endpoints)
4. [Social Endpoints](#social-endpoints)
5. [Achievement Endpoints](#achievement-endpoints)
6. [OAuth Endpoints](#oauth-endpoints)
7. [API Services](#api-services)
8. [<PERSON><PERSON><PERSON>ling](#error-handling)

## Authentication Endpoints

### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
    "username": "string",
    "email": "string",
    "password": "string",
    "confirm_password": "string",
    "first_name": "string (optional)",
    "last_name": "string (optional)"
}
```

**Response:**
- `302`: Redirect to dashboard on success
- `400`: Validation errors

### POST /auth/login
Authenticate user and create session.

**Request Body:**
```json
{
    "username": "string",
    "password": "string",
    "remember_me": "boolean (optional)"
}
```

**Response:**
- `302`: Redirect to dashboard on success
- `400`: Invalid credentials

### GET /auth/logout
Logout current user and destroy session.

**Response:**
- `302`: Redirect to home page

### GET /auth/profile
Get current user's profile information.

**Authentication:** Required

**Response:**
```json
{
    "user": {
        "id": "integer",
        "username": "string",
        "email": "string",
        "first_name": "string",
        "last_name": "string",
        "bio": "string",
        "created_at": "datetime"
    },
    "oauth_accounts": [
        {
            "provider": "string",
            "display_name": "string",
            "connected_at": "datetime"
        }
    ]
}
```

## Player Profile Endpoints

### GET /profiles
List all player profiles for current user.

**Authentication:** Required

**Response:**
```json
{
    "profiles": [
        {
            "id": "integer",
            "game_id": "string",
            "username": "string",
            "region": "string",
            "rank": "string",
            "last_updated": "datetime"
        }
    ]
}
```

### POST /profiles/add
Add a new player profile.

**Authentication:** Required

**Request Body:**
```json
{
    "game_id": "string",
    "username": "string",
    "region": "string"
}
```

**Response:**
- `302`: Redirect to profiles page on success
- `400`: Validation errors

### POST /profiles/<int:profile_id>/update
Update an existing player profile.

**Authentication:** Required

**Request Body:**
```json
{
    "username": "string",
    "region": "string"
}
```

**Response:**
- `302`: Redirect to profiles page on success
- `400`: Validation errors
- `404`: Profile not found

### POST /profiles/<int:profile_id>/delete
Delete a player profile.

**Authentication:** Required

**Response:**
- `302`: Redirect to profiles page
- `404`: Profile not found

## Stats Endpoints

### GET /stats/dashboard
Get dashboard statistics for current user.

**Authentication:** Required

**Response:**
```json
{
    "summary": {
        "total_matches": "integer",
        "total_wins": "integer",
        "total_losses": "integer",
        "win_rate": "float",
        "avg_kd_ratio": "float"
    },
    "recent_matches": [
        {
            "match_id": "string",
            "game": "string",
            "result": "string",
            "date": "datetime",
            "stats": {
                "kills": "integer",
                "deaths": "integer",
                "assists": "integer",
                "kd_ratio": "float"
            }
        }
    ],
    "achievements": [
        {
            "name": "string",
            "description": "string",
            "earned_at": "datetime"
        }
    ]
}
```

### GET /stats/profile/<int:profile_id>
Get detailed statistics for a specific player profile.

**Authentication:** Required

**Response:**
```json
{
    "profile": {
        "id": "integer",
        "game_id": "string",
        "username": "string",
        "region": "string"
    },
    "stats": {
        "total_matches": "integer",
        "wins": "integer",
        "losses": "integer",
        "win_rate": "float",
        "avg_kd_ratio": "float",
        "best_kd_ratio": "float",
        "total_kills": "integer",
        "total_deaths": "integer",
        "total_assists": "integer"
    },
    "recent_matches": [
        {
            "match_id": "string",
            "date": "datetime",
            "result": "string",
            "game_mode": "string",
            "duration": "integer",
            "stats": {
                "kills": "integer",
                "deaths": "integer",
                "assists": "integer",
                "score": "integer"
            }
        }
    ]
}
```

### POST /stats/sync/<int:profile_id>
Trigger manual stats synchronization for a profile.

**Authentication:** Required

**Response:**
```json
{
    "message": "string",
    "task_id": "string"
}
```

## Social Endpoints

### GET /social/friends
Get user's friends list and pending requests.

**Authentication:** Required

**Response:**
```json
{
    "friends": [
        {
            "id": "integer",
            "username": "string",
            "status": "string",
            "since": "datetime"
        }
    ],
    "pending_requests": [
        {
            "id": "integer",
            "username": "string",
            "sent_at": "datetime"
        }
    ]
}
```

### POST /social/friends/add
Send a friend request.

**Authentication:** Required

**Request Body:**
```json
{
    "username": "string"
}
```

**Response:**
- `302`: Redirect to friends page
- `400`: User not found or already friends

### POST /social/friends/<int:request_id>/accept
Accept a friend request.

**Authentication:** Required

**Response:**
- `302`: Redirect to friends page
- `404`: Request not found

### POST /social/friends/<int:friendship_id>/remove
Remove a friend.

**Authentication:** Required

**Response:**
- `302`: Redirect to friends page
- `404`: Friendship not found

### GET /social/search
Search for players.

**Authentication:** Required

**Query Parameters:**
- `q`: Search query (username)

**Response:**
```json
{
    "users": [
        {
            "id": "integer",
            "username": "string",
            "friendship_status": "string"
        }
    ]
}
```

### GET /social/compare/<int:user_id>
Compare stats with another user.

**Authentication:** Required

**Response:**
```json
{
    "comparison": {
        "user1": {
            "username": "string",
            "stats": {
                "total_matches": "integer",
                "win_rate": "float",
                "avg_kd_ratio": "float"
            }
        },
        "user2": {
            "username": "string",
            "stats": {
                "total_matches": "integer",
                "win_rate": "float",
                "avg_kd_ratio": "float"
            }
        }
    }
}
```

## Achievement Endpoints

### GET /achievements
Get all available achievements and user progress.

**Authentication:** Required

**Response:**
```json
{
    "achievements": [
        {
            "id": "integer",
            "name": "string",
            "description": "string",
            "category": "string",
            "icon": "string",
            "points": "integer",
            "earned": "boolean",
            "earned_at": "datetime (if earned)",
            "progress": "float (0-1)"
        }
    ]
}
```

## OAuth Endpoints

### GET /auth/oauth/<provider>
Initiate OAuth authentication flow.

**Authentication:** Required

**Parameters:**
- `provider`: OAuth provider (steam, riot)

**Response:**
- `302`: Redirect to OAuth provider

### GET /auth/oauth/<provider>/callback
Handle OAuth callback.

**Authentication:** Required

**Response:**
- `302`: Redirect to profile page

### POST /auth/oauth/<provider>/unlink
Unlink OAuth account.

**Authentication:** Required

**Response:**
- `302`: Redirect to profile page

## API Services

### Riot Games API Service

The application integrates with Riot Games API to fetch Valorant statistics.

**Endpoints Used:**
- Account API: Get player PUUID by Riot ID
- Match API: Get recent matches and match details
- Ranked API: Get competitive rankings

**Rate Limits:**
- Personal API Key: 100 requests every 2 minutes
- Production API Key: Higher limits available

### Steam Web API Service

Integration with Steam Web API for CS2 and Steam profile data.

**Endpoints Used:**
- ISteamUser/GetPlayerSummaries: Get player profile information
- IPlayerService/GetRecentlyPlayedGames: Get recently played games
- ISteamUserStats/GetUserStatsForGame: Get game statistics

**Rate Limits:**
- 100,000 requests per day per API key

## Error Handling

### HTTP Status Codes

- `200`: Success
- `302`: Redirect (successful form submission)
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (authentication required)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found
- `429`: Too Many Requests (rate limited)
- `500`: Internal Server Error

### Error Response Format

```json
{
    "error": {
        "code": "string",
        "message": "string",
        "details": "object (optional)"
    }
}
```

### Common Error Codes

- `VALIDATION_ERROR`: Form validation failed
- `AUTHENTICATION_REQUIRED`: User must be logged in
- `PERMISSION_DENIED`: User lacks required permissions
- `RESOURCE_NOT_FOUND`: Requested resource doesn't exist
- `RATE_LIMIT_EXCEEDED`: API rate limit exceeded
- `EXTERNAL_API_ERROR`: External API service error

## Authentication

The API uses session-based authentication with Flask-Login. Users must be logged in to access most endpoints.

### Session Management

- Sessions are stored server-side
- Session cookies are HTTP-only and secure
- Sessions expire after 30 days of inactivity
- Remember me functionality extends session lifetime

### CSRF Protection

All POST requests require CSRF tokens when using forms. CSRF tokens are automatically included in forms rendered by the application.
