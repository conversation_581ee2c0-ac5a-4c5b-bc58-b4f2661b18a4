# Game Stats Tracker - Deployment Guide

This guide covers deploying the Game Stats Tracker application to production environments.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Docker Deployment](#docker-deployment)
4. [Manual Deployment](#manual-deployment)
5. [Database Setup](#database-setup)
6. [SSL Configuration](#ssl-configuration)
7. [Monitoring and Logging](#monitoring-and-logging)
8. [Backup Strategy](#backup-strategy)

## Prerequisites

### System Requirements

- **OS**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **RAM**: Minimum 2GB, Recommended 4GB+
- **Storage**: Minimum 10GB, Recommended 50GB+
- **CPU**: 2+ cores recommended

### Required Software

- Docker and Docker Compose (recommended)
- Python 3.8+
- PostgreSQL 12+
- Redis 6+
- Nginx (for reverse proxy)
- SSL certificates (Let's Encrypt recommended)

## Environment Setup

### 1. Create Production Environment File

Create `.env.production`:

```env
# Flask Configuration
SECRET_KEY=your-super-secret-production-key-here
FLASK_ENV=production
DEBUG=False

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/game_stats_tracker_prod

# Redis
REDIS_URL=redis://localhost:6379/0

# Celery
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# API Keys
RIOT_API_KEY=your-production-riot-api-key
STEAM_API_KEY=your-production-steam-api-key

# OAuth Configuration
RIOT_OAUTH_CLIENT_ID=your-riot-oauth-client-id
RIOT_OAUTH_CLIENT_SECRET=your-riot-oauth-client-secret
STEAM_OPENID_REALM=https://yourdomain.com

# Mail Configuration
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Security
SSL_REDIRECT=true
SEND_FILE_MAX_AGE_DEFAULT=31536000
```

### 2. Security Considerations

- Use strong, unique passwords
- Enable firewall (UFW on Ubuntu)
- Keep system packages updated
- Use non-root user for application
- Enable fail2ban for SSH protection

## Docker Deployment

### 1. Docker Compose Setup

Create `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - FLASK_ENV=production
    env_file:
      - .env.production
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    command: gunicorn --bind 0.0.0.0:8000 --workers 4 --timeout 120 app:app

  worker:
    build: .
    environment:
      - FLASK_ENV=production
    env_file:
      - .env.production
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    command: celery -A app.celery worker --loglevel=info

  beat:
    build: .
    environment:
      - FLASK_ENV=production
    env_file:
      - .env.production
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    command: celery -A app.celery beat --loglevel=info

  db:
    image: postgres:13
    environment:
      POSTGRES_DB: game_stats_tracker_prod
      POSTGRES_USER: your_db_user
      POSTGRES_PASSWORD: your_db_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - ./static:/var/www/static
    depends_on:
      - web
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### 2. Nginx Configuration

Create `nginx.conf`:

```nginx
events {
    worker_connections 1024;
}

http {
    upstream app {
        server web:8000;
    }

    server {
        listen 80;
        server_name yourdomain.com www.yourdomain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name yourdomain.com www.yourdomain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;

        location / {
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /static/ {
            alias /var/www/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

### 3. Deploy with Docker

```bash
# Build and start services
docker-compose -f docker-compose.prod.yml up -d --build

# Run database migrations
docker-compose -f docker-compose.prod.yml exec web flask db upgrade

# Initialize application data
docker-compose -f docker-compose.prod.yml exec web flask init-db

# Check logs
docker-compose -f docker-compose.prod.yml logs -f
```

## Manual Deployment

### 1. System Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install dependencies
sudo apt install -y python3 python3-pip python3-venv postgresql postgresql-contrib redis-server nginx supervisor

# Create application user
sudo useradd -m -s /bin/bash gametracker
sudo usermod -aG sudo gametracker
```

### 2. Application Setup

```bash
# Switch to application user
sudo su - gametracker

# Clone repository
git clone <repository-url> /home/<USER>/game_stats_tracker
cd /home/<USER>/game_stats_tracker

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install gunicorn

# Set up environment
cp .env.example .env.production
# Edit .env.production with production values
```

### 3. Database Setup

```bash
# Create database user and database
sudo -u postgres psql
CREATE USER gametracker WITH PASSWORD 'your_password';
CREATE DATABASE game_stats_tracker_prod OWNER gametracker;
GRANT ALL PRIVILEGES ON DATABASE game_stats_tracker_prod TO gametracker;
\q

# Run migrations
source venv/bin/activate
export FLASK_APP=app.py
flask db upgrade
flask init-db
```

### 4. Supervisor Configuration

Create `/etc/supervisor/conf.d/gametracker.conf`:

```ini
[program:gametracker-web]
command=/home/<USER>/game_stats_tracker/venv/bin/gunicorn --bind 127.0.0.1:8000 --workers 4 app:app
directory=/home/<USER>/game_stats_tracker
user=gametracker
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/gametracker-web.log

[program:gametracker-worker]
command=/home/<USER>/game_stats_tracker/venv/bin/celery -A app.celery worker --loglevel=info
directory=/home/<USER>/game_stats_tracker
user=gametracker
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/gametracker-worker.log

[program:gametracker-beat]
command=/home/<USER>/game_stats_tracker/venv/bin/celery -A app.celery beat --loglevel=info
directory=/home/<USER>/game_stats_tracker
user=gametracker
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/gametracker-beat.log
```

### 5. Start Services

```bash
# Reload supervisor
sudo supervisorctl reread
sudo supervisorctl update

# Start services
sudo supervisorctl start gametracker-web
sudo supervisorctl start gametracker-worker
sudo supervisorctl start gametracker-beat

# Check status
sudo supervisorctl status
```

## SSL Configuration

### Using Let's Encrypt (Recommended)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Test auto-renewal
sudo certbot renew --dry-run
```

### Manual SSL Certificate

1. Obtain SSL certificate from your provider
2. Place certificate files in `/etc/nginx/ssl/`
3. Update Nginx configuration with correct paths
4. Test configuration: `sudo nginx -t`
5. Reload Nginx: `sudo systemctl reload nginx`

## Monitoring and Logging

### 1. Application Logs

```bash
# View application logs
sudo tail -f /var/log/gametracker-web.log
sudo tail -f /var/log/gametracker-worker.log
sudo tail -f /var/log/gametracker-beat.log

# View Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 2. System Monitoring

Install monitoring tools:

```bash
# Install htop for system monitoring
sudo apt install htop

# Install PostgreSQL monitoring
sudo apt install postgresql-contrib

# Monitor Redis
redis-cli info
```

### 3. Log Rotation

Create `/etc/logrotate.d/gametracker`:

```
/var/log/gametracker-*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 gametracker gametracker
    postrotate
        supervisorctl restart gametracker-web gametracker-worker gametracker-beat
    endscript
}
```

## Backup Strategy

### 1. Database Backup

Create backup script `/home/<USER>/backup_db.sh`:

```bash
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="game_stats_tracker_prod"

mkdir -p $BACKUP_DIR

# Create database backup
pg_dump $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/db_backup_$DATE.sql

# Remove backups older than 30 days
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +30 -delete

echo "Database backup completed: db_backup_$DATE.sql.gz"
```

### 2. Automated Backups

Add to crontab (`crontab -e`):

```bash
# Daily database backup at 2 AM
0 2 * * * /home/<USER>/backup_db.sh

# Weekly full backup at 3 AM on Sundays
0 3 * * 0 tar -czf /home/<USER>/backups/full_backup_$(date +\%Y\%m\%d).tar.gz /home/<USER>/game_stats_tracker --exclude=venv --exclude=__pycache__
```

### 3. Restore Procedure

```bash
# Restore database from backup
gunzip -c /home/<USER>/backups/db_backup_YYYYMMDD_HHMMSS.sql.gz | psql game_stats_tracker_prod

# Restore application files
tar -xzf /home/<USER>/backups/full_backup_YYYYMMDD.tar.gz -C /
```

## Maintenance

### Regular Tasks

1. **System Updates**: Monthly system package updates
2. **SSL Renewal**: Automatic with Let's Encrypt
3. **Log Cleanup**: Automatic with logrotate
4. **Database Maintenance**: Weekly VACUUM and ANALYZE
5. **Backup Verification**: Monthly restore tests

### Performance Optimization

1. **Database Indexing**: Monitor slow queries and add indexes
2. **Redis Memory**: Monitor Redis memory usage
3. **Application Metrics**: Use APM tools like New Relic or DataDog
4. **CDN**: Consider using CloudFlare for static assets

### Security Updates

1. Keep all system packages updated
2. Monitor security advisories for dependencies
3. Regular security scans
4. Review access logs for suspicious activity
5. Update API keys and secrets regularly
