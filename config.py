"""
Configuration settings for Game Stats Tracker
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Config:
    """Base configuration class"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # Database configuration
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'postgresql://localhost/game_stats_tracker'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Redis configuration
    REDIS_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/0'
    
    # Celery configuration
    CELERY_BROKER_URL = os.environ.get('CELERY_BROKER_URL') or 'redis://localhost:6379/0'
    CELERY_RESULT_BACKEND = os.environ.get('CELERY_RESULT_BACKEND') or 'redis://localhost:6379/0'

    # Redis Configuration
    REDIS_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/0'

    # Game API Keys
    RIOT_API_KEY = os.environ.get('RIOT_API_KEY')
    STEAM_API_KEY = os.environ.get('STEAM_API_KEY')

    # API Rate Limiting
    API_RATE_LIMIT_ENABLED = True
    API_CACHE_TIMEOUT = 300  # 5 minutes
    
    # Mail configuration
    MAIL_SERVER = os.environ.get('MAIL_SERVER') or 'localhost'
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # API Keys
    RIOT_API_KEY = os.environ.get('RIOT_API_KEY')
    STEAM_API_KEY = os.environ.get('STEAM_API_KEY')
    
    # OAuth Configuration
    STEAM_OAUTH_CLIENT_ID = os.environ.get('STEAM_OAUTH_CLIENT_ID')
    STEAM_OAUTH_CLIENT_SECRET = os.environ.get('STEAM_OAUTH_CLIENT_SECRET')
    
    RIOT_OAUTH_CLIENT_ID = os.environ.get('RIOT_OAUTH_CLIENT_ID')
    RIOT_OAUTH_CLIENT_SECRET = os.environ.get('RIOT_OAUTH_CLIENT_SECRET')
    
    # Cache settings
    CACHE_DEFAULT_TIMEOUT = 300  # 5 minutes
    STATS_CACHE_TIMEOUT = 1800   # 30 minutes
    
    # Background task settings
    STATS_UPDATE_INTERVAL = 3600  # 1 hour in seconds


class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or \
        'postgresql://localhost/game_stats_tracker_dev'


class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('TEST_DATABASE_URL') or \
        'postgresql://localhost/game_stats_tracker_test'
    WTF_CSRF_ENABLED = False


class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'postgresql://localhost/game_stats_tracker'
    
    # Additional production settings
    SSL_REDIRECT = True
    SEND_FILE_MAX_AGE_DEFAULT = 31536000  # 1 year
