version: "3.8"

services:
  web:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=**************************************/game_stats_tracker
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - .:/app
    command: flask run --host=0.0.0.0

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=game_stats_tracker
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  celery_worker:
    build: .
    command: python celery_worker.py worker --loglevel=info
    environment:
      - DATABASE_URL=**************************************/game_stats_tracker
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - .:/app

  celery_beat:
    build: .
    command: python celery_beat.py beat --loglevel=info
    environment:
      - DATABASE_URL=**************************************/game_stats_tracker
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - .:/app

volumes:
  postgres_data:
