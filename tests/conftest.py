"""
Test configuration and fixtures
"""
import pytest
import tempfile
import os
from app import create_app, db
from app.models.user import User
from app.models.game import Game
from app.models.player_profile import PlayerProfile
from app.models.achievement import Achievement
from app.models.oauth_account import OAuthAccount
from config import TestingConfig


@pytest.fixture
def app():
    """Create application for testing"""
    # Create temporary database
    db_fd, db_path = tempfile.mkstemp()
    
    # Override config for testing
    test_config = TestingConfig()
    test_config.SQLALCHEMY_DATABASE_URI = f'sqlite:///{db_path}'
    test_config.TESTING = True
    test_config.WTF_CSRF_ENABLED = False
    test_config.SECRET_KEY = 'test-secret-key'
    
    app = create_app(test_config)
    
    with app.app_context():
        db.create_all()
        
        # Create test games
        games = [
            Game(id='valorant', name='Valorant', api_endpoint='riot'),
            Game(id='cs2', name='Counter-Strike 2', api_endpoint='steam'),
            Game(id='bgmi', name='BGMI', api_endpoint='bgmi')
        ]
        
        for game in games:
            db.session.add(game)
        
        # Create test achievements
        achievements = [
            Achievement(
                name='First Victory',
                description='Win your first match',
                category='matches',
                icon='trophy',
                points=10,
                criteria={'wins': 1}
            ),
            Achievement(
                name='Sharpshooter',
                description='Achieve 2.0+ K/D ratio',
                category='performance',
                icon='crosshairs',
                points=25,
                criteria={'kd_ratio': 2.0}
            )
        ]
        
        for achievement in achievements:
            db.session.add(achievement)
        
        db.session.commit()
        
        yield app
        
        # Cleanup
        db.session.remove()
        db.drop_all()
        os.close(db_fd)
        os.unlink(db_path)


@pytest.fixture
def client(app):
    """Create test client"""
    return app.test_client()


@pytest.fixture
def runner(app):
    """Create test CLI runner"""
    return app.test_cli_runner()


@pytest.fixture
def test_user(app):
    """Create test user"""
    with app.app_context():
        user = User(
            username='testuser',
            email='<EMAIL>',
            first_name='Test',
            last_name='User'
        )
        user.set_password('testpassword')
        db.session.add(user)
        db.session.commit()
        return user


@pytest.fixture
def test_user2(app):
    """Create second test user"""
    with app.app_context():
        user = User(
            username='testuser2',
            email='<EMAIL>',
            first_name='Test2',
            last_name='User2'
        )
        user.set_password('testpassword2')
        db.session.add(user)
        db.session.commit()
        return user


@pytest.fixture
def authenticated_client(client, test_user):
    """Create authenticated test client"""
    with client.session_transaction() as sess:
        sess['_user_id'] = str(test_user.id)
        sess['_fresh'] = True
    return client


@pytest.fixture
def test_player_profile(app, test_user):
    """Create test player profile"""
    with app.app_context():
        profile = PlayerProfile(
            user_id=test_user.id,
            game_id='valorant',
            username='TestPlayer#1234',
            region='NA'
        )
        db.session.add(profile)
        db.session.commit()
        return profile


@pytest.fixture
def test_oauth_account(app, test_user):
    """Create test OAuth account"""
    with app.app_context():
        oauth_account = OAuthAccount(
            user_id=test_user.id,
            provider='steam',
            provider_user_id='*****************',
            user_info={
                'username': 'TestSteamUser',
                'profile_url': 'https://steamcommunity.com/id/testuser',
                'avatar': 'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/test.jpg'
            }
        )
        db.session.add(oauth_account)
        db.session.commit()
        return oauth_account


class AuthActions:
    """Helper class for authentication actions in tests"""
    
    def __init__(self, client):
        self._client = client
    
    def login(self, username='testuser', password='testpassword'):
        """Login user"""
        return self._client.post('/auth/login', data={
            'username': username,
            'password': password
        })
    
    def logout(self):
        """Logout user"""
        return self._client.get('/auth/logout')
    
    def register(self, username='newuser', email='<EMAIL>', password='newpassword'):
        """Register new user"""
        return self._client.post('/auth/register', data={
            'username': username,
            'email': email,
            'password': password,
            'confirm_password': password
        })


@pytest.fixture
def auth(client):
    """Authentication helper fixture"""
    return AuthActions(client)


# Mock data for API responses
MOCK_VALORANT_MATCH = {
    'matchInfo': {
        'matchId': 'test-match-id',
        'gameStartMillis': 1640995200000,
        'gameLengthMillis': 1800000,
        'queueId': 'competitive'
    },
    'players': [{
        'puuid': 'test-puuid',
        'gameName': 'TestPlayer',
        'tagLine': '1234',
        'stats': {
            'kills': 15,
            'deaths': 10,
            'assists': 5,
            'score': 3500
        }
    }]
}

MOCK_STEAM_PLAYER = {
    'response': {
        'players': [{
            'steamid': '*****************',
            'personaname': 'TestSteamUser',
            'profileurl': 'https://steamcommunity.com/id/testuser',
            'avatarfull': 'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/test.jpg'
        }]
    }
}


@pytest.fixture
def mock_api_responses():
    """Mock API responses for testing"""
    return {
        'valorant_match': MOCK_VALORANT_MATCH,
        'steam_player': MOCK_STEAM_PLAYER
    }
