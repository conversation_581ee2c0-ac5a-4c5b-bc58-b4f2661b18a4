"""
OAuth service for Steam and Riot ID integration
"""
import requests
import secrets
import hashlib
import hmac
import base64
from urllib.parse import urlencode, parse_qs, urlparse
from datetime import datetime, timedelta
from flask import current_app, session, url_for
from app import db
from app.models.user import User
from app.models.oauth_account import OAuthAccount
import logging

logger = logging.getLogger(__name__)


class OAuthService:
    """Base OAuth service class"""
    
    def __init__(self):
        self.client_id = None
        self.client_secret = None
        self.redirect_uri = None
        self.scope = None
        self.provider = None
    
    def get_authorization_url(self, state=None):
        """Get OAuth authorization URL"""
        raise NotImplementedError
    
    def exchange_code_for_token(self, code, state=None):
        """Exchange authorization code for access token"""
        raise NotImplementedError
    
    def get_user_info(self, access_token):
        """Get user information from OAuth provider"""
        raise NotImplementedError
    
    def link_account(self, user_id, provider_user_id, access_token, refresh_token=None, user_info=None):
        """Link OAuth account to user"""
        try:
            # Check if account already exists
            existing = OAuthAccount.query.filter_by(
                provider=self.provider,
                provider_user_id=str(provider_user_id)
            ).first()
            
            if existing:
                if existing.user_id != user_id:
                    return False, "This account is already linked to another user"
                
                # Update existing account
                existing.access_token = access_token
                existing.refresh_token = refresh_token
                existing.user_info = user_info
                existing.updated_at = datetime.utcnow()
            else:
                # Create new OAuth account
                oauth_account = OAuthAccount(
                    user_id=user_id,
                    provider=self.provider,
                    provider_user_id=str(provider_user_id),
                    access_token=access_token,
                    refresh_token=refresh_token,
                    user_info=user_info
                )
                db.session.add(oauth_account)
            
            db.session.commit()
            return True, "Account linked successfully"
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error linking {self.provider} account: {e}")
            return False, f"Error linking account: {str(e)}"


class SteamOAuthService(OAuthService):
    """Steam OpenID OAuth service"""
    
    def __init__(self):
        super().__init__()
        self.provider = 'steam'
        self.openid_url = 'https://steamcommunity.com/openid/login'
        self.api_key = current_app.config.get('STEAM_API_KEY')
        self.redirect_uri = url_for('auth.oauth_callback', provider='steam', _external=True)
    
    def get_authorization_url(self, state=None):
        """Get Steam OpenID authorization URL"""
        if state:
            session['oauth_state'] = state
        
        params = {
            'openid.ns': 'http://specs.openid.net/auth/2.0',
            'openid.mode': 'checkid_setup',
            'openid.return_to': self.redirect_uri,
            'openid.realm': current_app.config.get('SERVER_NAME', 'http://localhost:5000'),
            'openid.identity': 'http://specs.openid.net/auth/2.0/identifier_select',
            'openid.claimed_id': 'http://specs.openid.net/auth/2.0/identifier_select'
        }
        
        return f"{self.openid_url}?{urlencode(params)}"
    
    def verify_openid_response(self, args):
        """Verify Steam OpenID response"""
        try:
            # Prepare verification parameters
            params = dict(args)
            params['openid.mode'] = 'check_authentication'
            
            # Make verification request
            response = requests.post(self.openid_url, data=params, timeout=10)
            
            if response.text.strip() == 'is_valid:true':
                # Extract Steam ID from claimed_id
                claimed_id = args.get('openid.claimed_id', '')
                if claimed_id.startswith('https://steamcommunity.com/openid/id/'):
                    steam_id = claimed_id.split('/')[-1]
                    return True, steam_id
            
            return False, None
            
        except Exception as e:
            logger.error(f"Error verifying Steam OpenID response: {e}")
            return False, None
    
    def get_user_info(self, steam_id):
        """Get Steam user information"""
        if not self.api_key:
            logger.error("Steam API key not configured")
            return None
        
        try:
            url = 'http://api.steampowered.com/ISteamUser/GetPlayerSummaries/v0002/'
            params = {
                'key': self.api_key,
                'steamids': steam_id
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            players = data.get('response', {}).get('players', [])
            
            if players:
                player = players[0]
                return {
                    'steam_id': steam_id,
                    'username': player.get('personaname'),
                    'profile_url': player.get('profileurl'),
                    'avatar': player.get('avatarfull'),
                    'real_name': player.get('realname'),
                    'country': player.get('loccountrycode')
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting Steam user info: {e}")
            return None


class RiotOAuthService(OAuthService):
    """Riot Games OAuth service"""
    
    def __init__(self):
        super().__init__()
        self.provider = 'riot'
        self.client_id = current_app.config.get('RIOT_OAUTH_CLIENT_ID')
        self.client_secret = current_app.config.get('RIOT_OAUTH_CLIENT_SECRET')
        self.redirect_uri = url_for('auth.oauth_callback', provider='riot', _external=True)
        self.scope = 'openid'
        self.auth_url = 'https://auth.riotgames.com/authorize'
        self.token_url = 'https://auth.riotgames.com/token'
        self.userinfo_url = 'https://auth.riotgames.com/userinfo'
    
    def get_authorization_url(self, state=None):
        """Get Riot OAuth authorization URL"""
        if not state:
            state = secrets.token_urlsafe(32)
        
        session['oauth_state'] = state
        
        params = {
            'response_type': 'code',
            'client_id': self.client_id,
            'redirect_uri': self.redirect_uri,
            'scope': self.scope,
            'state': state
        }
        
        return f"{self.auth_url}?{urlencode(params)}"
    
    def exchange_code_for_token(self, code, state=None):
        """Exchange authorization code for access token"""
        if not self.client_id or not self.client_secret:
            logger.error("Riot OAuth credentials not configured")
            return None
        
        # Verify state
        if state and session.get('oauth_state') != state:
            logger.error("OAuth state mismatch")
            return None
        
        try:
            data = {
                'grant_type': 'authorization_code',
                'code': code,
                'redirect_uri': self.redirect_uri,
                'client_id': self.client_id,
                'client_secret': self.client_secret
            }
            
            response = requests.post(self.token_url, data=data, timeout=10)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.error(f"Error exchanging Riot OAuth code: {e}")
            return None
    
    def get_user_info(self, access_token):
        """Get Riot user information"""
        try:
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(self.userinfo_url, headers=headers, timeout=10)
            response.raise_for_status()
            
            user_info = response.json()
            
            # Get additional account info from Riot API
            account_info = self.get_riot_account_info(access_token)
            if account_info:
                user_info.update(account_info)
            
            return user_info
            
        except Exception as e:
            logger.error(f"Error getting Riot user info: {e}")
            return None
    
    def get_riot_account_info(self, access_token):
        """Get Riot account information"""
        try:
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            # Get account info
            response = requests.get(
                'https://americas.api.riotgames.com/riot/account/v1/accounts/me',
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                account_data = response.json()
                return {
                    'puuid': account_data.get('puuid'),
                    'game_name': account_data.get('gameName'),
                    'tag_line': account_data.get('tagLine')
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting Riot account info: {e}")
            return None


def get_oauth_service(provider):
    """Get OAuth service instance for provider"""
    if provider == 'steam':
        return SteamOAuthService()
    elif provider == 'riot':
        return RiotOAuthService()
    else:
        raise ValueError(f"Unsupported OAuth provider: {provider}")


def unlink_oauth_account(user_id, provider):
    """Unlink OAuth account from user"""
    try:
        oauth_account = OAuthAccount.query.filter_by(
            user_id=user_id,
            provider=provider
        ).first()
        
        if oauth_account:
            db.session.delete(oauth_account)
            db.session.commit()
            return True, "Account unlinked successfully"
        else:
            return False, "Account not found"
            
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error unlinking {provider} account: {e}")
        return False, f"Error unlinking account: {str(e)}"


def get_user_oauth_accounts(user_id):
    """Get all OAuth accounts for a user"""
    return OAuthAccount.query.filter_by(user_id=user_id).all()


def refresh_oauth_token(oauth_account):
    """Refresh OAuth access token"""
    if oauth_account.provider == 'riot' and oauth_account.refresh_token:
        try:
            service = get_oauth_service('riot')
            
            data = {
                'grant_type': 'refresh_token',
                'refresh_token': oauth_account.refresh_token,
                'client_id': service.client_id,
                'client_secret': service.client_secret
            }
            
            response = requests.post(service.token_url, data=data, timeout=10)
            response.raise_for_status()
            
            token_data = response.json()
            
            # Update OAuth account
            oauth_account.access_token = token_data.get('access_token')
            if 'refresh_token' in token_data:
                oauth_account.refresh_token = token_data.get('refresh_token')
            oauth_account.updated_at = datetime.utcnow()
            
            db.session.commit()
            return True, "Token refreshed successfully"
            
        except Exception as e:
            logger.error(f"Error refreshing OAuth token: {e}")
            return False, f"Error refreshing token: {str(e)}"
    
    return False, "Token refresh not supported for this provider"
