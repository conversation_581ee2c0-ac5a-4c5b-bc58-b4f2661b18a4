"""
Riot Games API service for Valorant
"""
from typing import Dict, List, Optional
from datetime import datetime
from flask import current_app
from app.services.base_api import BaseAPIService, APIException, PlayerNotFoundException
from app.utils.cache import cache_api_response


class RiotAPIService(BaseAPIService):
    """Service for Riot Games API (Valorant)"""
    
    def __init__(self, api_key: str):
        super().__init__(
            api_key=api_key,
            base_url='https://api.riotgames.com',
            rate_limit=100  # 100 requests per 2 minutes
        )
        
        # Regional endpoints
        self.regions = {
            'na': 'https://na.api.riotgames.com',
            'eu': 'https://europe.api.riotgames.com',
            'ap': 'https://ap.api.riotgames.com',
            'kr': 'https://kr.api.riotgames.com'
        }
        
        # Set API key header
        self.session.headers.update({
            'X-Riot-Token': self.api_key
        })
    
    def get_player_info(self, player_name: str, tag: str, region: str = 'na') -> Dict:
        """Get player information by name and tag"""
        if not self.validate_player_identifier(player_name) or not tag:
            raise APIException("Invalid player name or tag")
        
        try:
            # Get account info
            endpoint = f"riot/account/v1/accounts/by-riot-id/{player_name}/{tag}"
            account_data = self._make_request(endpoint, use_cache=True)
            
            if not account_data:
                raise PlayerNotFoundException(f"Player {player_name}#{tag} not found")
            
            puuid = account_data.get('puuid')
            if not puuid:
                raise APIException("Invalid account data received")
            
            # Get Valorant specific data
            valorant_data = self._get_valorant_player_data(puuid, region)
            
            return self.normalize_player_data({
                'player_id': puuid,
                'username': account_data.get('gameName'),
                'tag': account_data.get('tagLine'),
                'puuid': puuid,
                'region': region,
                **valorant_data
            })
            
        except Exception as e:
            current_app.logger.error(f"Error getting player info: {e}")
            raise APIException(f"Failed to get player info: {e}")
    
    def _get_valorant_player_data(self, puuid: str, region: str) -> Dict:
        """Get Valorant-specific player data"""
        try:
            # Get competitive updates (rank info)
            endpoint = f"val/ranked/v1/leaderboards/by-act/competitive"
            # Note: This is a simplified example. Actual Valorant API has different endpoints
            # for different types of data and may require different authentication
            
            return {
                'level': None,  # Valorant doesn't expose player level in public API
                'rank': 'Unknown',  # Would need to parse from competitive data
                'rating': 0,
                'last_updated': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            current_app.logger.warning(f"Could not get Valorant player data: {e}")
            return {}
    
    @cache_api_response('riot', 'recent_matches', timeout=300)  # 5 minutes
    def get_recent_matches(self, puuid: str, count: int = 20) -> List[Dict]:
        """Get recent matches for a player"""
        if not puuid:
            raise APIException("PUUID is required")
        
        try:
            # Get match history
            endpoint = f"val/match/v1/matchlists/by-puuid/{puuid}"
            params = {'size': min(count, 20)}  # API limit
            
            match_list = self._make_request(endpoint, params=params)
            
            if not match_list or 'history' not in match_list:
                return []
            
            matches = []
            for match_info in match_list['history']:
                match_id = match_info.get('matchId')
                if match_id:
                    try:
                        match_details = self.get_match_details(match_id)
                        if match_details:
                            # Find player's data in the match
                            player_match_data = self._extract_player_match_data(match_details, puuid)
                            if player_match_data:
                                matches.append(self.normalize_match_data(player_match_data))
                    except Exception as e:
                        current_app.logger.warning(f"Could not get match details for {match_id}: {e}")
                        continue
            
            return matches
            
        except Exception as e:
            current_app.logger.error(f"Error getting recent matches: {e}")
            raise APIException(f"Failed to get recent matches: {e}")
    
    def get_match_details(self, match_id: str) -> Dict:
        """Get detailed match information"""
        if not match_id:
            raise APIException("Match ID is required")
        
        try:
            endpoint = f"val/match/v1/matches/{match_id}"
            match_data = self._make_request(endpoint, use_cache=True)
            
            return match_data
            
        except Exception as e:
            current_app.logger.error(f"Error getting match details: {e}")
            raise APIException(f"Failed to get match details: {e}")
    
    def _extract_player_match_data(self, match_details: Dict, puuid: str) -> Optional[Dict]:
        """Extract player-specific data from match details"""
        if not match_details or 'players' not in match_details:
            return None
        
        # Find the player in the match
        for player in match_details['players']:
            if player.get('puuid') == puuid:
                stats = player.get('stats', {})
                
                return {
                    'match_id': match_details.get('matchInfo', {}).get('matchId'),
                    'date': self._parse_match_date(match_details.get('matchInfo', {}).get('gameStartMillis')),
                    'duration': match_details.get('matchInfo', {}).get('gameLengthMillis', 0) // 1000,
                    'map': match_details.get('matchInfo', {}).get('mapId'),
                    'mode': match_details.get('matchInfo', {}).get('queueId'),
                    'result': self._determine_match_result(match_details, player),
                    'kills': stats.get('kills', 0),
                    'deaths': stats.get('deaths', 0),
                    'assists': stats.get('assists', 0),
                    'score': stats.get('score', 0),
                    'rank_change': None,  # Would need additional API calls
                    'agent': player.get('characterId'),
                    'team': player.get('teamId'),
                    'raw_data': player
                }
        
        return None
    
    def _parse_match_date(self, timestamp_ms: int) -> str:
        """Parse match timestamp to ISO format"""
        if not timestamp_ms:
            return datetime.utcnow().isoformat()
        
        try:
            dt = datetime.fromtimestamp(timestamp_ms / 1000)
            return dt.isoformat()
        except (ValueError, TypeError):
            return datetime.utcnow().isoformat()
    
    def _determine_match_result(self, match_details: Dict, player: Dict) -> str:
        """Determine if the player won or lost the match"""
        try:
            teams = match_details.get('teams', [])
            player_team_id = player.get('teamId')
            
            for team in teams:
                if team.get('teamId') == player_team_id:
                    if team.get('won'):
                        return 'win'
                    else:
                        return 'loss'
            
            return 'unknown'
            
        except Exception:
            return 'unknown'
    
    @cache_api_response('riot', 'player_stats', timeout=600)  # 10 minutes
    def get_player_stats(self, puuid: str) -> Dict:
        """Get aggregated player statistics"""
        if not puuid:
            raise APIException("PUUID is required")
        
        try:
            # Get recent matches and calculate stats
            recent_matches = self.get_recent_matches(puuid, 50)
            
            if not recent_matches:
                return {
                    'total_matches': 0,
                    'wins': 0,
                    'losses': 0,
                    'win_rate': 0.0,
                    'avg_kills': 0.0,
                    'avg_deaths': 0.0,
                    'avg_assists': 0.0,
                    'kd_ratio': 0.0
                }
            
            # Calculate statistics
            total_matches = len(recent_matches)
            wins = len([m for m in recent_matches if m.get('result') == 'win'])
            losses = len([m for m in recent_matches if m.get('result') == 'loss'])
            
            total_kills = sum(m.get('kills', 0) for m in recent_matches)
            total_deaths = sum(m.get('deaths', 0) for m in recent_matches)
            total_assists = sum(m.get('assists', 0) for m in recent_matches)
            
            return {
                'total_matches': total_matches,
                'wins': wins,
                'losses': losses,
                'win_rate': (wins / total_matches * 100) if total_matches > 0 else 0.0,
                'avg_kills': total_kills / total_matches if total_matches > 0 else 0.0,
                'avg_deaths': total_deaths / total_matches if total_matches > 0 else 0.0,
                'avg_assists': total_assists / total_matches if total_matches > 0 else 0.0,
                'kd_ratio': total_kills / total_deaths if total_deaths > 0 else float(total_kills),
                'total_kills': total_kills,
                'total_deaths': total_deaths,
                'total_assists': total_assists
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting player stats: {e}")
            raise APIException(f"Failed to get player stats: {e}")
    
    def validate_player_identifier(self, identifier: str) -> bool:
        """Validate Riot ID format"""
        if not identifier or not identifier.strip():
            return False
        
        # Basic validation - Riot IDs should be reasonable length
        return 3 <= len(identifier.strip()) <= 16
