"""
Game API Services Package

This package contains API service classes for integrating with various game APIs:
- Riot Games API (Valorant)
- Steam Web API (CS2)
- BGMI (placeholder implementation)

Usage:
    from app.services import get_valorant_service, get_cs2_service, get_bgmi_service

    # Get specific service
    valorant_api = get_valorant_service()

    # Get service by game name
    from app.services.api_factory import get_service_for_game
    service = get_service_for_game('valorant')
"""

from app.services.api_factory import (
    APIServiceFactory,
    get_valorant_service,
    get_cs2_service,
    get_bgmi_service,
    get_service_for_game,
    check_all_services
)

from app.services.base_api import (
    BaseAPIService,
    APIException,
    RateLimitException,
    PlayerNotFoundException
)

__all__ = [
    'APIServiceFactory',
    'get_valorant_service',
    'get_cs2_service',
    'get_bgmi_service',
    'get_service_for_game',
    'check_all_services',
    'BaseAPIService',
    'APIException',
    'RateLimitException',
    'PlayerNotFoundException'
]