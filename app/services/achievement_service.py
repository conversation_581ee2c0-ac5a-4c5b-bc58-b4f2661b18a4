"""
Achievement Service for managing user achievements and progress tracking
"""
from datetime import datetime, timedelta
from sqlalchemy import func
from app import db
from app.models.achievement import Achievement, UserAchievement
from app.models.match import Match
from app.models.player_stats import PlayerStats
from app.models.player_profile import PlayerProfile
from app.utils.cache import cached
import logging

logger = logging.getLogger(__name__)


class AchievementService:
    """Service for managing achievements and progress tracking"""
    
    @staticmethod
    def initialize_achievements():
        """Initialize default achievements in the database"""
        default_achievements = [
            # Match-based achievements
            {
                'name': 'First Victory',
                'description': 'Win your first match',
                'category': 'matches',
                'icon': 'trophy',
                'points': 10,
                'criteria': {'wins': 1}
            },
            {
                'name': 'Winning Streak',
                'description': 'Win 5 matches in a row',
                'category': 'matches',
                'icon': 'fire',
                'points': 25,
                'criteria': {'win_streak': 5}
            },
            {
                'name': 'Century Club',
                'description': 'Play 100 matches',
                'category': 'matches',
                'icon': 'gamepad',
                'points': 50,
                'criteria': {'total_matches': 100}
            },
            
            # Performance-based achievements
            {
                'name': 'Sharpshooter',
                'description': 'Achieve a K/D ratio of 2.0 or higher',
                'category': 'performance',
                'icon': 'crosshairs',
                'points': 30,
                'criteria': {'kd_ratio': 2.0}
            },
            {
                'name': 'Ace',
                'description': 'Get 5 kills in a single match',
                'category': 'performance',
                'icon': 'star',
                'points': 20,
                'criteria': {'single_match_kills': 5}
            },
            {
                'name': 'Consistent Performer',
                'description': 'Maintain above 60% win rate over 50 matches',
                'category': 'performance',
                'icon': 'chart-line',
                'points': 40,
                'criteria': {'win_rate': 0.6, 'min_matches': 50}
            },
            
            # Time-based achievements
            {
                'name': 'Daily Grinder',
                'description': 'Play matches for 7 consecutive days',
                'category': 'activity',
                'icon': 'calendar-check',
                'points': 35,
                'criteria': {'consecutive_days': 7}
            },
            {
                'name': 'Weekend Warrior',
                'description': 'Play 10 matches in a weekend',
                'category': 'activity',
                'icon': 'clock',
                'points': 25,
                'criteria': {'weekend_matches': 10}
            },
            
            # Multi-game achievements
            {
                'name': 'Multi-Gamer',
                'description': 'Have active profiles in 3 different games',
                'category': 'diversity',
                'icon': 'users',
                'points': 30,
                'criteria': {'active_games': 3}
            },
            {
                'name': 'Jack of All Trades',
                'description': 'Win matches in 3 different games',
                'category': 'diversity',
                'icon': 'medal',
                'points': 45,
                'criteria': {'games_with_wins': 3}
            }
        ]
        
        for achievement_data in default_achievements:
            existing = Achievement.query.filter_by(name=achievement_data['name']).first()
            if not existing:
                achievement = Achievement(**achievement_data)
                db.session.add(achievement)
        
        try:
            db.session.commit()
            logger.info("Default achievements initialized successfully")
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error initializing achievements: {e}")
    
    @staticmethod
    def check_user_achievements(user_id):
        """Check and award new achievements for a user"""
        try:
            # Get all achievements that user hasn't earned yet
            earned_achievement_ids = db.session.query(UserAchievement.achievement_id).filter_by(
                user_id=user_id
            ).subquery()
            
            pending_achievements = Achievement.query.filter(
                ~Achievement.id.in_(earned_achievement_ids)
            ).all()
            
            newly_earned = []
            
            for achievement in pending_achievements:
                if AchievementService._check_achievement_criteria(user_id, achievement):
                    # Award the achievement
                    user_achievement = UserAchievement(
                        user_id=user_id,
                        achievement_id=achievement.id,
                        earned_at=datetime.utcnow()
                    )
                    db.session.add(user_achievement)
                    newly_earned.append(achievement)
            
            if newly_earned:
                db.session.commit()
                logger.info(f"Awarded {len(newly_earned)} new achievements to user {user_id}")
            
            return newly_earned
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error checking achievements for user {user_id}: {e}")
            return []
    
    @staticmethod
    def _check_achievement_criteria(user_id, achievement):
        """Check if user meets the criteria for a specific achievement"""
        criteria = achievement.criteria
        
        try:
            if achievement.category == 'matches':
                return AchievementService._check_match_criteria(user_id, criteria)
            elif achievement.category == 'performance':
                return AchievementService._check_performance_criteria(user_id, criteria)
            elif achievement.category == 'activity':
                return AchievementService._check_activity_criteria(user_id, criteria)
            elif achievement.category == 'diversity':
                return AchievementService._check_diversity_criteria(user_id, criteria)
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking criteria for achievement {achievement.name}: {e}")
            return False
    
    @staticmethod
    def _check_match_criteria(user_id, criteria):
        """Check match-based achievement criteria"""
        # Get user's profiles
        profiles = PlayerProfile.query.filter_by(user_id=user_id).all()
        profile_ids = [p.id for p in profiles]
        
        if not profile_ids:
            return False
        
        # Total wins
        if 'wins' in criteria:
            total_wins = Match.query.filter(
                Match.player_profile_id.in_(profile_ids),
                Match.result == 'win'
            ).count()
            if total_wins < criteria['wins']:
                return False
        
        # Total matches
        if 'total_matches' in criteria:
            total_matches = Match.query.filter(
                Match.player_profile_id.in_(profile_ids)
            ).count()
            if total_matches < criteria['total_matches']:
                return False
        
        # Win streak
        if 'win_streak' in criteria:
            # This is a simplified check - in practice, you'd want to track streaks more carefully
            recent_matches = Match.query.filter(
                Match.player_profile_id.in_(profile_ids)
            ).order_by(Match.match_date.desc()).limit(criteria['win_streak']).all()
            
            if len(recent_matches) < criteria['win_streak']:
                return False
            
            if not all(match.result == 'win' for match in recent_matches):
                return False
        
        return True
    
    @staticmethod
    def _check_performance_criteria(user_id, criteria):
        """Check performance-based achievement criteria"""
        profiles = PlayerProfile.query.filter_by(user_id=user_id).all()
        profile_ids = [p.id for p in profiles]
        
        if not profile_ids:
            return False
        
        # K/D ratio
        if 'kd_ratio' in criteria:
            avg_kd = db.session.query(func.avg(PlayerStats.kd_ratio)).join(Match).filter(
                Match.player_profile_id.in_(profile_ids),
                PlayerStats.kd_ratio.isnot(None)
            ).scalar()
            
            if not avg_kd or avg_kd < criteria['kd_ratio']:
                return False
        
        # Single match kills
        if 'single_match_kills' in criteria:
            max_kills = db.session.query(func.max(PlayerStats.kills)).join(Match).filter(
                Match.player_profile_id.in_(profile_ids)
            ).scalar()
            
            if not max_kills or max_kills < criteria['single_match_kills']:
                return False
        
        # Win rate with minimum matches
        if 'win_rate' in criteria and 'min_matches' in criteria:
            total_matches = Match.query.filter(
                Match.player_profile_id.in_(profile_ids)
            ).count()
            
            if total_matches < criteria['min_matches']:
                return False
            
            wins = Match.query.filter(
                Match.player_profile_id.in_(profile_ids),
                Match.result == 'win'
            ).count()
            
            win_rate = wins / total_matches if total_matches > 0 else 0
            if win_rate < criteria['win_rate']:
                return False
        
        return True
    
    @staticmethod
    def _check_activity_criteria(user_id, criteria):
        """Check activity-based achievement criteria"""
        profiles = PlayerProfile.query.filter_by(user_id=user_id).all()
        profile_ids = [p.id for p in profiles]
        
        if not profile_ids:
            return False
        
        # Consecutive days (simplified check)
        if 'consecutive_days' in criteria:
            # Check if user has matches in the last N consecutive days
            days_to_check = criteria['consecutive_days']
            today = datetime.utcnow().date()
            
            for i in range(days_to_check):
                check_date = today - timedelta(days=i)
                matches_on_date = Match.query.filter(
                    Match.player_profile_id.in_(profile_ids),
                    func.date(Match.match_date) == check_date
                ).count()
                
                if matches_on_date == 0:
                    return False
        
        # Weekend matches
        if 'weekend_matches' in criteria:
            # Check matches in the current or last weekend
            today = datetime.utcnow().date()
            days_since_saturday = (today.weekday() + 2) % 7
            last_saturday = today - timedelta(days=days_since_saturday)
            last_sunday = last_saturday + timedelta(days=1)
            
            weekend_matches = Match.query.filter(
                Match.player_profile_id.in_(profile_ids),
                func.date(Match.match_date).between(last_saturday, last_sunday)
            ).count()
            
            if weekend_matches < criteria['weekend_matches']:
                return False
        
        return True
    
    @staticmethod
    def _check_diversity_criteria(user_id, criteria):
        """Check diversity-based achievement criteria"""
        profiles = PlayerProfile.query.filter_by(user_id=user_id).all()
        
        # Active games
        if 'active_games' in criteria:
            active_games = len([p for p in profiles if p.is_active])
            if active_games < criteria['active_games']:
                return False
        
        # Games with wins
        if 'games_with_wins' in criteria:
            games_with_wins = set()
            for profile in profiles:
                wins = Match.query.filter_by(
                    player_profile_id=profile.id,
                    result='win'
                ).count()
                if wins > 0:
                    games_with_wins.add(profile.game_id)
            
            if len(games_with_wins) < criteria['games_with_wins']:
                return False
        
        return True
    
    @staticmethod
    @cached(timeout=300, key_prefix='achievements:user')
    def get_user_achievements(user_id):
        """Get all achievements earned by a user"""
        return UserAchievement.query.filter_by(user_id=user_id).join(Achievement).all()
    
    @staticmethod
    @cached(timeout=600, key_prefix='achievements:progress')
    def get_achievement_progress(user_id, achievement_id):
        """Get progress towards a specific achievement"""
        achievement = Achievement.query.get(achievement_id)
        if not achievement:
            return None
        
        # This would return progress percentage and current values
        # Implementation depends on specific achievement criteria
        return {
            'achievement_id': achievement_id,
            'progress_percentage': 0,  # Calculate based on criteria
            'current_value': 0,
            'target_value': 0
        }
