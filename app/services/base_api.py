"""
Base API service class for game integrations
"""
import requests
import time
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import redis
import json
from app import redis_client
from flask import current_app


class BaseAPIService(ABC):
    """Base class for all game API services"""
    
    def __init__(self, api_key: str, base_url: str, rate_limit: int = 100):
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.rate_limit = rate_limit  # requests per minute
        self.session = requests.Session()
        self.cache_timeout = 300  # 5 minutes default cache
        
        # Set up session headers
        self.session.headers.update({
            'User-Agent': 'GameStatsTracker/1.0',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
    
    def _get_cache_key(self, endpoint: str, params: Dict = None) -> str:
        """Generate cache key for API requests"""
        key_parts = [self.__class__.__name__, endpoint]
        if params:
            # Sort params for consistent cache keys
            sorted_params = sorted(params.items())
            key_parts.append(str(sorted_params))
        return ':'.join(key_parts)
    
    def _get_from_cache(self, cache_key: str) -> Optional[Dict]:
        """Get data from Redis cache"""
        try:
            cached_data = redis_client.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
        except Exception as e:
            current_app.logger.warning(f"Cache get error: {e}")
        return None
    
    def _set_cache(self, cache_key: str, data: Dict, timeout: int = None) -> None:
        """Set data in Redis cache"""
        try:
            timeout = timeout or self.cache_timeout
            redis_client.setex(cache_key, timeout, json.dumps(data))
        except Exception as e:
            current_app.logger.warning(f"Cache set error: {e}")
    
    def _make_request(self, endpoint: str, params: Dict = None, use_cache: bool = True) -> Dict:
        """Make HTTP request with caching and rate limiting"""
        # Check cache first
        if use_cache:
            cache_key = self._get_cache_key(endpoint, params)
            cached_data = self._get_from_cache(cache_key)
            if cached_data:
                return cached_data
        
        # Rate limiting
        self._rate_limit()
        
        # Make request
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # Cache successful responses
            if use_cache and response.status_code == 200:
                self._set_cache(cache_key, data)
            
            return data
            
        except requests.exceptions.RequestException as e:
            current_app.logger.error(f"API request failed: {url} - {e}")
            raise APIException(f"API request failed: {e}")
        except json.JSONDecodeError as e:
            current_app.logger.error(f"Invalid JSON response: {e}")
            raise APIException(f"Invalid JSON response: {e}")
    
    def _rate_limit(self) -> None:
        """Implement rate limiting"""
        # Simple rate limiting using Redis
        rate_key = f"rate_limit:{self.__class__.__name__}"
        try:
            current_requests = redis_client.get(rate_key)
            if current_requests is None:
                redis_client.setex(rate_key, 60, 1)  # 1 minute window
            else:
                current_requests = int(current_requests)
                if current_requests >= self.rate_limit:
                    time.sleep(1)  # Wait 1 second if rate limit exceeded
                else:
                    redis_client.incr(rate_key)
        except Exception as e:
            current_app.logger.warning(f"Rate limiting error: {e}")
    
    @abstractmethod
    def get_player_info(self, player_identifier: str) -> Dict:
        """Get basic player information"""
        pass
    
    @abstractmethod
    def get_recent_matches(self, player_identifier: str, count: int = 20) -> List[Dict]:
        """Get recent matches for a player"""
        pass
    
    @abstractmethod
    def get_player_stats(self, player_identifier: str) -> Dict:
        """Get player statistics"""
        pass
    
    @abstractmethod
    def get_match_details(self, match_id: str) -> Dict:
        """Get detailed match information"""
        pass
    
    def validate_player_identifier(self, identifier: str) -> bool:
        """Validate player identifier format"""
        return bool(identifier and identifier.strip())
    
    def normalize_match_data(self, raw_match: Dict) -> Dict:
        """Normalize match data to common format"""
        return {
            'match_id': raw_match.get('match_id'),
            'date': raw_match.get('date'),
            'duration': raw_match.get('duration'),
            'map': raw_match.get('map'),
            'mode': raw_match.get('mode'),
            'result': raw_match.get('result'),
            'kills': raw_match.get('kills', 0),
            'deaths': raw_match.get('deaths', 0),
            'assists': raw_match.get('assists', 0),
            'score': raw_match.get('score', 0),
            'rank_change': raw_match.get('rank_change'),
            'raw_data': raw_match
        }
    
    def normalize_player_data(self, raw_player: Dict) -> Dict:
        """Normalize player data to common format"""
        return {
            'player_id': raw_player.get('player_id'),
            'username': raw_player.get('username'),
            'tag': raw_player.get('tag'),
            'level': raw_player.get('level'),
            'rank': raw_player.get('rank'),
            'rating': raw_player.get('rating'),
            'region': raw_player.get('region'),
            'raw_data': raw_player
        }


class APIException(Exception):
    """Custom exception for API errors"""
    pass


class RateLimitException(APIException):
    """Exception for rate limit errors"""
    pass


class PlayerNotFoundException(APIException):
    """Exception when player is not found"""
    pass
