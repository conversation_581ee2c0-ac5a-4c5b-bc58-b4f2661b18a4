"""
Steam Web API service for CS2 and other Steam games
"""
from typing import Dict, List, Optional
from datetime import datetime
from flask import current_app
from app.services.base_api import BaseAPIService, APIException, PlayerNotFoundException
from app.utils.cache import cache_api_response


class SteamAPIService(BaseAPIService):
    """Service for Steam Web API (CS2, etc.)"""
    
    def __init__(self, api_key: str):
        super().__init__(
            api_key=api_key,
            base_url='https://api.steampowered.com',
            rate_limit=100000  # Steam has generous rate limits
        )
        
        # Game IDs
        self.game_ids = {
            'cs2': 730,  # Counter-Strike 2
            'csgo': 730,  # Counter-Strike: Global Offensive (same app ID)
            'dota2': 570,
            'tf2': 440
        }
    
    def get_player_info(self, steam_id: str) -> Dict:
        """Get player information by Steam ID"""
        if not self.validate_player_identifier(steam_id):
            raise APIException("Invalid Steam ID")
        
        try:
            # Convert Steam ID to 64-bit format if needed
            steam_id_64 = self._convert_to_steam_id_64(steam_id)
            
            # Get player summaries
            endpoint = "ISteamUser/GetPlayerSummaries/v0002/"
            params = {
                'key': self.api_key,
                'steamids': steam_id_64
            }
            
            response = self._make_request(endpoint, params=params)
            
            if not response or 'response' not in response or 'players' not in response['response']:
                raise PlayerNotFoundException(f"Player with Steam ID {steam_id} not found")
            
            players = response['response']['players']
            if not players:
                raise PlayerNotFoundException(f"Player with Steam ID {steam_id} not found")
            
            player_data = players[0]
            
            # Get CS2 stats if available
            cs2_stats = self._get_cs2_stats(steam_id_64)
            
            return self.normalize_player_data({
                'player_id': steam_id_64,
                'username': player_data.get('personaname'),
                'tag': None,  # Steam doesn't use tags
                'level': self._get_steam_level(steam_id_64),
                'rank': cs2_stats.get('rank'),
                'rating': cs2_stats.get('rating'),
                'region': self._get_player_country(player_data),
                'avatar_url': player_data.get('avatarfull'),
                'profile_url': player_data.get('profileurl'),
                'last_logoff': player_data.get('lastlogoff'),
                'raw_data': player_data
            })
            
        except Exception as e:
            current_app.logger.error(f"Error getting Steam player info: {e}")
            raise APIException(f"Failed to get Steam player info: {e}")
    
    def _get_cs2_stats(self, steam_id_64: str) -> Dict:
        """Get CS2-specific statistics"""
        try:
            # Get CS2 user stats
            endpoint = "ISteamUserStats/GetUserStatsForGame/v0002/"
            params = {
                'key': self.api_key,
                'steamid': steam_id_64,
                'appid': self.game_ids['cs2']
            }
            
            response = self._make_request(endpoint, params=params, use_cache=True)
            
            if not response or 'playerstats' not in response:
                return {}
            
            stats = response['playerstats'].get('stats', [])
            
            # Parse relevant stats
            parsed_stats = {}
            for stat in stats:
                name = stat.get('name', '')
                value = stat.get('value', 0)
                
                if 'total_kills' in name:
                    parsed_stats['total_kills'] = value
                elif 'total_deaths' in name:
                    parsed_stats['total_deaths'] = value
                elif 'total_wins' in name:
                    parsed_stats['total_wins'] = value
                elif 'total_matches_played' in name:
                    parsed_stats['total_matches'] = value
            
            return parsed_stats
            
        except Exception as e:
            current_app.logger.warning(f"Could not get CS2 stats: {e}")
            return {}
    
    def _get_steam_level(self, steam_id_64: str) -> Optional[int]:
        """Get Steam level for a user"""
        try:
            endpoint = "IPlayerService/GetSteamLevel/v1/"
            params = {
                'key': self.api_key,
                'steamid': steam_id_64
            }
            
            response = self._make_request(endpoint, params=params, use_cache=True)
            
            if response and 'response' in response:
                return response['response'].get('player_level')
            
        except Exception as e:
            current_app.logger.warning(f"Could not get Steam level: {e}")
        
        return None
    
    def _get_player_country(self, player_data: Dict) -> Optional[str]:
        """Extract player country from profile data"""
        return player_data.get('loccountrycode')
    
    def get_recent_matches(self, steam_id: str, count: int = 20) -> List[Dict]:
        """Get recent matches for a player"""
        # Note: Steam Web API doesn't provide match history for CS2
        # This would typically require third-party services or game-specific APIs
        
        current_app.logger.warning("Steam Web API doesn't provide match history for CS2")
        return []
    
    def get_match_details(self, match_id: str) -> Dict:
        """Get detailed match information"""
        # Note: Steam Web API doesn't provide detailed match data for CS2
        current_app.logger.warning("Steam Web API doesn't provide match details for CS2")
        return {}
    
    @cache_api_response('steam', 'player_stats', timeout=600)  # 10 minutes
    def get_player_stats(self, steam_id: str) -> Dict:
        """Get aggregated player statistics"""
        if not self.validate_player_identifier(steam_id):
            raise APIException("Invalid Steam ID")
        
        try:
            steam_id_64 = self._convert_to_steam_id_64(steam_id)
            cs2_stats = self._get_cs2_stats(steam_id_64)
            
            if not cs2_stats:
                return {
                    'total_matches': 0,
                    'wins': 0,
                    'losses': 0,
                    'win_rate': 0.0,
                    'total_kills': 0,
                    'total_deaths': 0,
                    'kd_ratio': 0.0
                }
            
            total_matches = cs2_stats.get('total_matches', 0)
            total_wins = cs2_stats.get('total_wins', 0)
            total_kills = cs2_stats.get('total_kills', 0)
            total_deaths = cs2_stats.get('total_deaths', 0)
            
            return {
                'total_matches': total_matches,
                'wins': total_wins,
                'losses': total_matches - total_wins if total_matches > total_wins else 0,
                'win_rate': (total_wins / total_matches * 100) if total_matches > 0 else 0.0,
                'total_kills': total_kills,
                'total_deaths': total_deaths,
                'kd_ratio': total_kills / total_deaths if total_deaths > 0 else float(total_kills),
                'avg_kills': total_kills / total_matches if total_matches > 0 else 0.0,
                'avg_deaths': total_deaths / total_matches if total_matches > 0 else 0.0
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting Steam player stats: {e}")
            raise APIException(f"Failed to get Steam player stats: {e}")
    
    def _convert_to_steam_id_64(self, steam_id: str) -> str:
        """Convert various Steam ID formats to 64-bit Steam ID"""
        # Remove any whitespace
        steam_id = steam_id.strip()
        
        # If it's already a 64-bit Steam ID (17 digits starting with 7656119)
        if steam_id.isdigit() and len(steam_id) == 17 and steam_id.startswith('7656119'):
            return steam_id
        
        # If it's a Steam ID in STEAM_0:X:Y format
        if steam_id.startswith('STEAM_'):
            try:
                parts = steam_id.split(':')
                if len(parts) == 3:
                    y = int(parts[2])
                    x = int(parts[1])
                    steam_id_64 = str(***************** + y * 2 + x)
                    return steam_id_64
            except (ValueError, IndexError):
                pass
        
        # If it's a Steam ID3 format [U:1:X]
        if steam_id.startswith('[U:1:') and steam_id.endswith(']'):
            try:
                account_id = int(steam_id[5:-1])
                steam_id_64 = str(***************** + account_id)
                return steam_id_64
            except ValueError:
                pass
        
        # If it's just the account ID (numeric)
        if steam_id.isdigit() and len(steam_id) < 17:
            try:
                account_id = int(steam_id)
                steam_id_64 = str(***************** + account_id)
                return steam_id_64
            except ValueError:
                pass
        
        # If none of the above, assume it's already in the correct format
        return steam_id
    
    def validate_player_identifier(self, identifier: str) -> bool:
        """Validate Steam ID format"""
        if not identifier or not identifier.strip():
            return False
        
        identifier = identifier.strip()
        
        # Check various Steam ID formats
        formats = [
            # 64-bit Steam ID
            lambda x: x.isdigit() and len(x) == 17 and x.startswith('7656119'),
            # STEAM_0:X:Y format
            lambda x: x.startswith('STEAM_') and len(x.split(':')) == 3,
            # Steam ID3 format [U:1:X]
            lambda x: x.startswith('[U:1:') and x.endswith(']'),
            # Account ID (numeric)
            lambda x: x.isdigit() and 1 <= len(x) <= 10
        ]
        
        return any(fmt(identifier) for fmt in formats)
    
    def get_owned_games(self, steam_id: str) -> List[Dict]:
        """Get list of games owned by the player"""
        try:
            steam_id_64 = self._convert_to_steam_id_64(steam_id)
            
            endpoint = "IPlayerService/GetOwnedGames/v0001/"
            params = {
                'key': self.api_key,
                'steamid': steam_id_64,
                'include_appinfo': True,
                'include_played_free_games': True
            }
            
            response = self._make_request(endpoint, params=params, use_cache=True)
            
            if response and 'response' in response and 'games' in response['response']:
                return response['response']['games']
            
            return []
            
        except Exception as e:
            current_app.logger.error(f"Error getting owned games: {e}")
            return []
