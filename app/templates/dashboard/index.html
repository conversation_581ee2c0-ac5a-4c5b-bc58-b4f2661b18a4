{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-tachometer-alt me-2"></i>Dashboard</h1>
    <div>
        <span class="text-muted">Welcome back, {{ current_user.get_full_name() }}!</span>
    </div>
</div>

<!-- Stats Overview -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_matches }}</h4>
                        <p class="mb-0">Total Matches</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-gamepad fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_wins }}</h4>
                        <p class="mb-0">Total Wins</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-trophy fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ "%.1f"|format(overall_win_rate) }}%</h4>
                        <p class="mb-0">Win Rate</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-percentage fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ profiles|length }}</h4>
                        <p class="mb-0">Active Profiles</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Game Profiles -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-user-circle me-2"></i>Game Profiles</h5>
                <a href="{{ url_for('dashboard.profiles') }}" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                {% if profiles %}
                    {% for profile in profiles %}
                    <div class="d-flex justify-content-between align-items-center mb-3 p-2 border rounded">
                        <div>
                            <h6 class="mb-1">{{ profile.get_full_player_name() }}</h6>
                            <small class="text-muted">{{ profile.game.name }}</small>
                        </div>
                        <div class="text-end">
                            <div class="badge bg-primary">{{ profile.get_rank_display() }}</div>
                            <div class="small text-muted">{{ profile.total_matches }} matches</div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">No game profiles yet. <a href="{{ url_for('dashboard.profiles') }}">Add your first profile</a></p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Recent Matches -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Matches</h5>
                <a href="{{ url_for('dashboard.matches') }}" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                {% if recent_matches %}
                    {% for match in recent_matches %}
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                        <div>
                            <span class="badge bg-{{ 'success' if match.result == 'win' else 'danger' }}">
                                {{ match.result.upper() }}
                            </span>
                            <small class="ms-2">{{ match.player_profile.game.name }}</small>
                        </div>
                        <div class="text-end">
                            <div class="small">{{ match.kills }}/{{ match.deaths }}/{{ match.assists }}</div>
                            <div class="small text-muted">{{ match.match_date.strftime('%m/%d') }}</div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">No recent matches found.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Achievements -->
{% if recent_achievements %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-medal me-2"></i>Recent Achievements</h5>
                <a href="{{ url_for('dashboard.achievements') }}" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for user_achievement in recent_achievements %}
                    <div class="col-md-4 mb-3">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-trophy fa-2x text-warning mb-2"></i>
                                <h6>{{ user_achievement.achievement.name }}</h6>
                                <p class="small text-muted">{{ user_achievement.achievement.description }}</p>
                                <small class="text-muted">{{ user_achievement.unlocked_at.strftime('%B %d, %Y') }}</small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
