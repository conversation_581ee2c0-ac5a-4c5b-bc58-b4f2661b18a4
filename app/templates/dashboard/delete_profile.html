{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>Delete Profile
                </h4>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-warning me-2"></i>
                    <strong>Warning!</strong> This action cannot be undone.
                </div>
                
                <p>You are about to delete your <strong>{{ profile.game.name }}</strong> profile:</p>
                
                <div class="card bg-light mb-3">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-4"><strong>Game:</strong></div>
                            <div class="col-sm-8">{{ profile.game.name }}</div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4"><strong>Player Name:</strong></div>
                            <div class="col-sm-8">
                                {{ profile.player_name }}
                                {% if profile.player_tag %}#{{ profile.player_tag }}{% endif %}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4"><strong>Region:</strong></div>
                            <div class="col-sm-8">{{ profile.region.upper() }}</div>
                        </div>
                        {% if profile.total_matches %}
                        <div class="row">
                            <div class="col-sm-4"><strong>Total Matches:</strong></div>
                            <div class="col-sm-8">{{ profile.total_matches }}</div>
                        </div>
                        {% endif %}
                        <div class="row">
                            <div class="col-sm-4"><strong>Created:</strong></div>
                            <div class="col-sm-8">{{ profile.created_at.strftime('%Y-%m-%d') }}</div>
                        </div>
                    </div>
                </div>
                
                <p class="text-danger">
                    <strong>This will permanently delete:</strong>
                </p>
                <ul class="text-danger">
                    <li>All match history associated with this profile</li>
                    <li>All statistics and performance data</li>
                    <li>Any achievements earned through this profile</li>
                    <li>Profile settings and preferences</li>
                </ul>
                
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.confirm.label(class="form-label") }}
                        {{ form.confirm(class="form-control" + (" is-invalid" if form.confirm.errors else ""), placeholder="Type DELETE to confirm") }}
                        {% if form.confirm.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.confirm.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Type "DELETE" exactly to confirm deletion</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('dashboard.edit_profile', profile_id=profile.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Cancel
                        </a>
                        {{ form.submit(class="btn btn-danger") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmInput = document.getElementById('confirm');
    const submitButton = document.querySelector('button[type="submit"]');
    
    function updateSubmitButton() {
        if (confirmInput.value === 'DELETE') {
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="fas fa-trash me-2"></i>Delete Profile';
        } else {
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-trash me-2"></i>Type DELETE to confirm';
        }
    }
    
    confirmInput.addEventListener('input', updateSubmitButton);
    updateSubmitButton(); // Initial call
});
</script>
{% endblock %}
