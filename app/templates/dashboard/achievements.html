{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-trophy me-2 text-warning"></i>Achievements
    </h2>
    <div class="text-muted">
        <i class="fas fa-star me-1"></i>{{ total_points }} Points
    </div>
</div>

<!-- Achievement Stats -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h3>{{ earned_count }}</h3>
                <p class="mb-0">Earned</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h3>{{ total_achievements }}</h3>
                <p class="mb-0">Total</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h3>{{ "%.1f"|format(completion_percentage) }}%</h3>
                <p class="mb-0">Complete</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h3>{{ total_points }}</h3>
                <p class="mb-0">Points</p>
            </div>
        </div>
    </div>
</div>

<!-- Achievement Categories -->
<div class="row">
    <div class="col-12">
        <ul class="nav nav-tabs mb-4" id="achievementTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab">
                    All ({{ total_achievements }})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="earned-tab" data-bs-toggle="tab" data-bs-target="#earned" type="button" role="tab">
                    Earned ({{ earned_count }})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="matches-tab" data-bs-toggle="tab" data-bs-target="#matches" type="button" role="tab">
                    Matches
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="performance-tab" data-bs-toggle="tab" data-bs-target="#performance" type="button" role="tab">
                    Performance
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activity" type="button" role="tab">
                    Activity
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="diversity-tab" data-bs-toggle="tab" data-bs-target="#diversity" type="button" role="tab">
                    Diversity
                </button>
            </li>
        </ul>
        
        <div class="tab-content" id="achievementTabContent">
            <!-- All Achievements -->
            <div class="tab-pane fade show active" id="all" role="tabpanel">
                <div class="row">
                    {% for achievement in all_achievements %}
                    <div class="col-lg-4 col-md-6 mb-4">
                        {% set is_earned = achievement.id in earned_achievement_ids %}
                        <div class="card h-100 {% if is_earned %}border-success{% else %}border-secondary{% endif %}">
                            <div class="card-body d-flex flex-column">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="achievement-icon me-3">
                                        <i class="fas fa-{{ achievement.icon }} fa-2x {% if is_earned %}text-warning{% else %}text-muted{% endif %}"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h5 class="card-title mb-1 {% if not is_earned %}text-muted{% endif %}">
                                            {{ achievement.name }}
                                        </h5>
                                        <small class="text-muted">{{ achievement.category.title() }}</small>
                                    </div>
                                    {% if is_earned %}
                                    <div class="text-success">
                                        <i class="fas fa-check-circle fa-lg"></i>
                                    </div>
                                    {% endif %}
                                </div>
                                
                                <p class="card-text flex-grow-1 {% if not is_earned %}text-muted{% endif %}">
                                    {{ achievement.description }}
                                </p>
                                
                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="badge {% if is_earned %}bg-success{% else %}bg-secondary{% endif %}">
                                            {{ achievement.points }} points
                                        </span>
                                        {% if is_earned %}
                                        <small class="text-success">
                                            <i class="fas fa-calendar me-1"></i>
                                            {{ user_achievements[achievement.id].earned_at.strftime('%m/%d/%Y') }}
                                        </small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Earned Achievements -->
            <div class="tab-pane fade" id="earned" role="tabpanel">
                <div class="row">
                    {% for user_achievement in earned_achievements %}
                    {% set achievement = user_achievement.achievement %}
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 border-success">
                            <div class="card-body d-flex flex-column">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="achievement-icon me-3">
                                        <i class="fas fa-{{ achievement.icon }} fa-2x text-warning"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h5 class="card-title mb-1">{{ achievement.name }}</h5>
                                        <small class="text-muted">{{ achievement.category.title() }}</small>
                                    </div>
                                    <div class="text-success">
                                        <i class="fas fa-check-circle fa-lg"></i>
                                    </div>
                                </div>
                                
                                <p class="card-text flex-grow-1">{{ achievement.description }}</p>
                                
                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="badge bg-success">{{ achievement.points }} points</span>
                                        <small class="text-success">
                                            <i class="fas fa-calendar me-1"></i>
                                            {{ user_achievement.earned_at.strftime('%m/%d/%Y') }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    
                    {% if not earned_achievements %}
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Achievements Earned Yet</h5>
                            <p class="text-muted">Start playing and tracking your stats to earn your first achievement!</p>
                            <a href="{{ url_for('dashboard.profiles') }}" class="btn btn-primary">
                                <i class="fas fa-gamepad me-2"></i>View Profiles
                            </a>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Category-specific tabs -->
            {% for category in ['matches', 'performance', 'activity', 'diversity'] %}
            <div class="tab-pane fade" id="{{ category }}" role="tabpanel">
                <div class="row">
                    {% for achievement in all_achievements %}
                    {% if achievement.category == category %}
                    {% set is_earned = achievement.id in earned_achievement_ids %}
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 {% if is_earned %}border-success{% else %}border-secondary{% endif %}">
                            <div class="card-body d-flex flex-column">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="achievement-icon me-3">
                                        <i class="fas fa-{{ achievement.icon }} fa-2x {% if is_earned %}text-warning{% else %}text-muted{% endif %}"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h5 class="card-title mb-1 {% if not is_earned %}text-muted{% endif %}">
                                            {{ achievement.name }}
                                        </h5>
                                    </div>
                                    {% if is_earned %}
                                    <div class="text-success">
                                        <i class="fas fa-check-circle fa-lg"></i>
                                    </div>
                                    {% endif %}
                                </div>
                                
                                <p class="card-text flex-grow-1 {% if not is_earned %}text-muted{% endif %}">
                                    {{ achievement.description }}
                                </p>
                                
                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="badge {% if is_earned %}bg-success{% else %}bg-secondary{% endif %}">
                                            {{ achievement.points }} points
                                        </span>
                                        {% if is_earned %}
                                        <small class="text-success">
                                            <i class="fas fa-calendar me-1"></i>
                                            {{ user_achievements[achievement.id].earned_at.strftime('%m/%d/%Y') }}
                                        </small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<style>
.achievement-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0,0,0,0.05);
    border-radius: 50%;
}

.card.border-success {
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.2);
}

.card.border-secondary {
    opacity: 0.8;
}
</style>
{% endblock %}
