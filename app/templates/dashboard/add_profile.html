{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-plus me-2"></i>Add Game Profile
                </h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.game_id.label(class="form-label") }}
                            {{ form.game_id(class="form-select" + (" is-invalid" if form.game_id.errors else "")) }}
                            {% if form.game_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.game_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.region.label(class="form-label") }}
                            {{ form.region(class="form-select" + (" is-invalid" if form.region.errors else "")) }}
                            {% if form.region.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.region.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            {{ form.player_name.label(class="form-label") }}
                            {{ form.player_name(class="form-control" + (" is-invalid" if form.player_name.errors else ""), placeholder="Enter your in-game username") }}
                            {% if form.player_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.player_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            {{ form.player_tag.label(class="form-label") }}
                            {{ form.player_tag(class="form-control" + (" is-invalid" if form.player_tag.errors else ""), placeholder="Tag (for Valorant)") }}
                            {% if form.player_tag.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.player_tag.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Required for Valorant (e.g., #1234)</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.external_id.label(class="form-label") }}
                        {{ form.external_id(class="form-control" + (" is-invalid" if form.external_id.errors else ""), placeholder="Steam ID, PUUID, etc. (optional)") }}
                        {% if form.external_id.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.external_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            For Steam games: Your Steam ID (e.g., 76561198000000000)<br>
                            For Valorant: Will be automatically retrieved
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else ""), rows="3", placeholder="Any additional notes about this profile...") }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('dashboard.profiles') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Cancel
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Help Section -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>How to find your game identifiers
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6><i class="fas fa-crosshairs me-2"></i>Valorant</h6>
                        <p class="small">
                            Your Riot ID consists of your username and tag (e.g., PlayerName#1234).
                            You can find this in your Valorant client or Riot account settings.
                        </p>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-bomb me-2"></i>CS2</h6>
                        <p class="small">
                            You can use your Steam profile URL, Steam ID, or custom URL.
                            Find your Steam ID at <a href="https://steamid.io/" target="_blank">steamid.io</a>
                        </p>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-mobile-alt me-2"></i>BGMI</h6>
                        <p class="small">
                            Use your in-game player ID or username.
                            You can find this in your BGMI profile settings.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const gameSelect = document.getElementById('game_id');
    const playerTagField = document.getElementById('player_tag');
    const externalIdField = document.getElementById('external_id');
    
    function updateFieldRequirements() {
        const selectedOption = gameSelect.options[gameSelect.selectedIndex];
        const gameText = selectedOption.text.toLowerCase();
        
        if (gameText.includes('valorant')) {
            playerTagField.required = true;
            playerTagField.parentElement.querySelector('.form-text').style.display = 'block';
        } else {
            playerTagField.required = false;
            playerTagField.parentElement.querySelector('.form-text').style.display = 'none';
        }
    }
    
    gameSelect.addEventListener('change', updateFieldRequirements);
    updateFieldRequirements(); // Initial call
});
</script>
{% endblock %}
