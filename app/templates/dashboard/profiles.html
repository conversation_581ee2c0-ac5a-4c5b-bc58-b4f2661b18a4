{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>My Game Profiles</h2>
    <div class="btn-group">
        <a href="{{ url_for('dashboard.add_profile') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Add Profile
        </a>
        {% if profiles %}
        <form method="POST" action="{{ url_for('dashboard.update_all_profiles_stats') }}" class="d-inline">
            <button type="submit" class="btn btn-outline-success">
                <i class="fas fa-sync-alt me-2"></i>Update All Stats
            </button>
        </form>
        {% endif %}
    </div>
</div>

{% if profiles %}
<div class="row">
    {% for profile in profiles %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100 {% if not profile.is_active %}border-secondary{% endif %}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    {% if profile.game.name == 'Valorant' %}
                        <i class="fas fa-crosshairs text-danger me-2"></i>
                    {% elif profile.game.name == 'CS2' %}
                        <i class="fas fa-bomb text-warning me-2"></i>
                    {% elif profile.game.name == 'BGMI' %}
                        <i class="fas fa-mobile-alt text-info me-2"></i>
                    {% else %}
                        <i class="fas fa-gamepad text-primary me-2"></i>
                    {% endif %}
                    <strong>{{ profile.game.name }}</strong>
                </div>
                {% if not profile.is_active %}
                    <span class="badge bg-secondary">Inactive</span>
                {% endif %}
            </div>
            <div class="card-body">
                <h5 class="card-title">
                    {{ profile.player_name }}
                    {% if profile.player_tag %}
                        <small class="text-muted">#{{ profile.player_tag }}</small>
                    {% endif %}
                </h5>
                
                <div class="mb-3">
                    <small class="text-muted">
                        <i class="fas fa-globe me-1"></i>{{ profile.region.upper() }}
                    </small>
                </div>
                
                {% if profile.total_matches %}
                <div class="row text-center mb-3">
                    <div class="col-4">
                        <div class="text-primary">
                            <strong>{{ profile.total_matches }}</strong>
                        </div>
                        <small class="text-muted">Matches</small>
                    </div>
                    <div class="col-4">
                        <div class="text-success">
                            <strong>{{ profile.wins or 0 }}</strong>
                        </div>
                        <small class="text-muted">Wins</small>
                    </div>
                    <div class="col-4">
                        <div class="text-info">
                            <strong>{{ "%.1f"|format(profile.win_rate * 100) if profile.win_rate else "0.0" }}%</strong>
                        </div>
                        <small class="text-muted">Win Rate</small>
                    </div>
                </div>
                {% else %}
                <div class="text-center mb-3">
                    <div class="text-muted">
                        <i class="fas fa-chart-line me-2"></i>No stats available yet
                    </div>
                    <small class="text-muted">Click "Update Stats" to fetch data</small>
                </div>
                {% endif %}
                
                {% if profile.notes %}
                <div class="mb-3">
                    <small class="text-muted">
                        <i class="fas fa-sticky-note me-1"></i>{{ profile.notes[:50] }}{% if profile.notes|length > 50 %}...{% endif %}
                    </small>
                </div>
                {% endif %}
                
                {% if profile.last_updated %}
                <div class="mb-3">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>Updated {{ profile.last_updated.strftime('%m/%d/%Y') }}
                    </small>
                </div>
                {% endif %}
            </div>
            <div class="card-footer">
                <div class="btn-group w-100" role="group">
                    <a href="{{ url_for('dashboard.profile_detail', profile_id=profile.id) }}"
                       class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>View
                    </a>
                    <a href="{{ url_for('dashboard.edit_profile', profile_id=profile.id) }}"
                       class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-edit me-1"></i>Edit
                    </a>
                </div>
                <div class="btn-group w-100 mt-2" role="group">
                    <form method="POST" action="{{ url_for('dashboard.update_profile_stats_route', profile_id=profile.id) }}"
                          class="flex-fill">
                        <button type="submit" class="btn btn-outline-success btn-sm w-100">
                            <i class="fas fa-sync me-1"></i>Update Stats
                        </button>
                    </form>
                    <form method="POST" action="{{ url_for('dashboard.fetch_profile_matches', profile_id=profile.id) }}"
                          class="flex-fill">
                        <button type="submit" class="btn btn-outline-info btn-sm w-100">
                            <i class="fas fa-download me-1"></i>Fetch Matches
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

{% if available_games %}
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-plus-circle me-2"></i>Available Games
        </h5>
    </div>
    <div class="card-body">
        <p class="text-muted mb-3">You can add profiles for these games:</p>
        <div class="row">
            {% for game in available_games %}
                {% set has_profile = profiles|selectattr('game_id', 'equalto', game.id)|list|length > 0 %}
                {% if not has_profile %}
                <div class="col-md-4 mb-2">
                    <div class="d-flex align-items-center">
                        {% if game.name == 'Valorant' %}
                            <i class="fas fa-crosshairs text-danger me-2"></i>
                        {% elif game.name == 'CS2' %}
                            <i class="fas fa-bomb text-warning me-2"></i>
                        {% elif game.name == 'BGMI' %}
                            <i class="fas fa-mobile-alt text-info me-2"></i>
                        {% else %}
                            <i class="fas fa-gamepad text-primary me-2"></i>
                        {% endif %}
                        <span>{{ game.name }}</span>
                    </div>
                </div>
                {% endif %}
            {% endfor %}
        </div>
        <a href="{{ url_for('dashboard.add_profile') }}" class="btn btn-primary mt-2">
            <i class="fas fa-plus me-2"></i>Add New Profile
        </a>
    </div>
</div>
{% endif %}

{% else %}
<!-- Empty State -->
<div class="text-center py-5">
    <div class="mb-4">
        <i class="fas fa-gamepad fa-4x text-muted"></i>
    </div>
    <h4 class="text-muted">No Game Profiles Yet</h4>
    <p class="text-muted mb-4">
        Start tracking your gaming performance by adding your first game profile.
    </p>
    <a href="{{ url_for('dashboard.add_profile') }}" class="btn btn-primary btn-lg">
        <i class="fas fa-plus me-2"></i>Add Your First Profile
    </a>
</div>

<!-- Available Games -->
{% if available_games %}
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-gamepad me-2"></i>Supported Games
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            {% for game in available_games %}
            <div class="col-md-4 mb-3">
                <div class="card border-0 bg-light">
                    <div class="card-body text-center">
                        {% if game.name == 'Valorant' %}
                            <i class="fas fa-crosshairs fa-2x text-danger mb-2"></i>
                        {% elif game.name == 'CS2' %}
                            <i class="fas fa-bomb fa-2x text-warning mb-2"></i>
                        {% elif game.name == 'BGMI' %}
                            <i class="fas fa-mobile-alt fa-2x text-info mb-2"></i>
                        {% else %}
                            <i class="fas fa-gamepad fa-2x text-primary mb-2"></i>
                        {% endif %}
                        <h6>{{ game.name }}</h6>
                        <small class="text-muted">{{ game.description or 'Track your performance' }}</small>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}
{% endif %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add loading state to update buttons
    const updateButtons = document.querySelectorAll('button[type="submit"]');
    updateButtons.forEach(button => {
        button.addEventListener('click', function() {
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Updating...';
            this.disabled = true;
            
            // Re-enable after 5 seconds (in case of error)
            setTimeout(() => {
                this.innerHTML = originalText;
                this.disabled = false;
            }, 5000);
        });
    });
});
</script>
{% endblock %}
