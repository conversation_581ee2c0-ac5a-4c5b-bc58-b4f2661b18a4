{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-edit me-2"></i>Edit {{ profile.game.name }} Profile
                </h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Game</label>
                            <input type="text" class="form-control" value="{{ profile.game.name }}" readonly>
                            <div class="form-text">Game cannot be changed after creation</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.region.label(class="form-label") }}
                            {{ form.region(class="form-select" + (" is-invalid" if form.region.errors else "")) }}
                            {% if form.region.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.region.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            {{ form.player_name.label(class="form-label") }}
                            {{ form.player_name(class="form-control" + (" is-invalid" if form.player_name.errors else "")) }}
                            {% if form.player_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.player_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            {{ form.player_tag.label(class="form-label") }}
                            {{ form.player_tag(class="form-control" + (" is-invalid" if form.player_tag.errors else "")) }}
                            {% if form.player_tag.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.player_tag.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.external_id.label(class="form-label") }}
                        {{ form.external_id(class="form-control" + (" is-invalid" if form.external_id.errors else "")) }}
                        {% if form.external_id.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.external_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else ""), rows="3") }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.is_active.label(class="form-label") }}
                        {{ form.is_active(class="form-select" + (" is-invalid" if form.is_active.errors else "")) }}
                        {% if form.is_active.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.is_active.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Inactive profiles won't be updated automatically</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('dashboard.profiles') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Cancel
                        </a>
                        <div>
                            <a href="{{ url_for('dashboard.delete_profile', profile_id=profile.id) }}" 
                               class="btn btn-outline-danger me-2">
                                <i class="fas fa-trash me-2"></i>Delete
                            </a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Profile Stats -->
        <div class="card mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Profile Statistics
                </h5>
                <form method="POST" action="{{ url_for('dashboard.update_profile_stats', profile_id=profile.id) }}" class="d-inline">
                    <button type="submit" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-sync me-1"></i>Update Stats
                    </button>
                </form>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-primary">{{ profile.total_matches or 0 }}</h4>
                            <small class="text-muted">Total Matches</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-success">{{ profile.wins or 0 }}</h4>
                            <small class="text-muted">Wins</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-danger">{{ profile.losses or 0 }}</h4>
                            <small class="text-muted">Losses</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-info">{{ "%.1f"|format(profile.win_rate * 100) if profile.win_rate else "0.0" }}%</h4>
                            <small class="text-muted">Win Rate</small>
                        </div>
                    </div>
                </div>
                
                {% if profile.last_updated %}
                <div class="mt-3 text-center">
                    <small class="text-muted">
                        Last updated: {{ profile.last_updated.strftime('%Y-%m-%d %H:%M UTC') }}
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
