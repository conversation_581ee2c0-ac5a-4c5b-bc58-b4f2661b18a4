{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Performance Analytics</h2>
    <div class="btn-group">
        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
            <i class="fas fa-filter me-2"></i>Filter
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#" data-filter="7">Last 7 days</a></li>
            <li><a class="dropdown-item" href="#" data-filter="30">Last 30 days</a></li>
            <li><a class="dropdown-item" href="#" data-filter="90">Last 90 days</a></li>
            <li><a class="dropdown-item" href="#" data-filter="all">All time</a></li>
        </ul>
    </div>
</div>

<!-- Overview Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_matches }}</h4>
                        <p class="card-text">Total Matches</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-gamepad fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ "%.1f"|format(overall_win_rate * 100) }}%</h4>
                        <p class="card-text">Win Rate</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-trophy fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ "%.2f"|format(overall_kd_ratio) }}</h4>
                        <p class="card-text">K/D Ratio</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-crosshairs fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ active_profiles }}</h4>
                        <p class="card-text">Active Games</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Win Rate Trend -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>Win Rate Trend
                </h5>
            </div>
            <div class="card-body">
                <canvas id="winRateChart" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <!-- K/D Ratio Trend -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-area me-2"></i>K/D Ratio Trend
                </h5>
            </div>
            <div class="card-body">
                <canvas id="kdChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Game Performance Breakdown -->
<div class="row mb-4">
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Performance by Game
                </h5>
            </div>
            <div class="card-body">
                <canvas id="gamePerformanceChart" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>Match Results
                </h5>
            </div>
            <div class="card-body">
                <canvas id="matchResultsChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Matches Table -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-history me-2"></i>Recent Matches
        </h5>
    </div>
    <div class="card-body">
        {% if recent_matches %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Game</th>
                        <th>Date</th>
                        <th>Map</th>
                        <th>Result</th>
                        <th>K/D/A</th>
                        <th>Score</th>
                        <th>Duration</th>
                    </tr>
                </thead>
                <tbody>
                    {% for match in recent_matches %}
                    <tr>
                        <td>
                            {% if match.player_profile.game.name == 'Valorant' %}
                                <i class="fas fa-crosshairs text-danger me-1"></i>
                            {% elif match.player_profile.game.name == 'CS2' %}
                                <i class="fas fa-bomb text-warning me-1"></i>
                            {% elif match.player_profile.game.name == 'BGMI' %}
                                <i class="fas fa-mobile-alt text-info me-1"></i>
                            {% endif %}
                            {{ match.player_profile.game.name }}
                        </td>
                        <td>{{ match.match_date.strftime('%m/%d %H:%M') }}</td>
                        <td>{{ match.map_name }}</td>
                        <td>
                            {% if match.result == 'win' %}
                                <span class="badge bg-success">Win</span>
                            {% elif match.result == 'loss' %}
                                <span class="badge bg-danger">Loss</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ match.result.title() }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if match.player_stats %}
                                {{ match.player_stats[0].kills }}/{{ match.player_stats[0].deaths }}/{{ match.player_stats[0].assists }}
                            {% else %}
                                -/-/-
                            {% endif %}
                        </td>
                        <td>
                            {% if match.player_stats %}
                                {{ match.player_stats[0].score }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>{{ (match.duration_seconds // 60) if match.duration_seconds else '-' }}m</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No Match Data Available</h5>
            <p class="text-muted">Start playing and fetch your match history to see detailed analytics.</p>
            <a href="{{ url_for('dashboard.profiles') }}" class="btn btn-primary">
                <i class="fas fa-sync me-2"></i>Fetch Match Data
            </a>
        </div>
        {% endif %}
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Chart.js configuration and data
const chartData = {
    winRateData: {{ win_rate_data | tojson }},
    kdData: {{ kd_data | tojson }},
    gamePerformanceData: {{ game_performance_data | tojson }},
    matchResultsData: {{ match_results_data | tojson }}
};

// Win Rate Trend Chart
const winRateCtx = document.getElementById('winRateChart').getContext('2d');
const winRateChart = new Chart(winRateCtx, {
    type: 'line',
    data: {
        labels: chartData.winRateData.labels,
        datasets: [{
            label: 'Win Rate %',
            data: chartData.winRateData.data,
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 100
            }
        }
    }
});

// K/D Ratio Chart
const kdCtx = document.getElementById('kdChart').getContext('2d');
const kdChart = new Chart(kdCtx, {
    type: 'line',
    data: {
        labels: chartData.kdData.labels,
        datasets: [{
            label: 'K/D Ratio',
            data: chartData.kdData.data,
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Game Performance Chart
const gamePerformanceCtx = document.getElementById('gamePerformanceChart').getContext('2d');
const gamePerformanceChart = new Chart(gamePerformanceCtx, {
    type: 'bar',
    data: {
        labels: chartData.gamePerformanceData.labels,
        datasets: [{
            label: 'Win Rate %',
            data: chartData.gamePerformanceData.winRates,
            backgroundColor: 'rgba(54, 162, 235, 0.8)'
        }, {
            label: 'K/D Ratio',
            data: chartData.gamePerformanceData.kdRatios,
            backgroundColor: 'rgba(255, 206, 86, 0.8)',
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                max: 100
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                grid: {
                    drawOnChartArea: false,
                }
            }
        }
    }
});

// Match Results Pie Chart
const matchResultsCtx = document.getElementById('matchResultsChart').getContext('2d');
const matchResultsChart = new Chart(matchResultsCtx, {
    type: 'doughnut',
    data: {
        labels: chartData.matchResultsData.labels,
        datasets: [{
            data: chartData.matchResultsData.data,
            backgroundColor: [
                'rgba(75, 192, 192, 0.8)',
                'rgba(255, 99, 132, 0.8)',
                'rgba(255, 206, 86, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

// Filter functionality
document.querySelectorAll('[data-filter]').forEach(button => {
    button.addEventListener('click', function(e) {
        e.preventDefault();
        const filter = this.dataset.filter;
        // Reload page with filter parameter
        window.location.href = `{{ url_for('dashboard.stats') }}?filter=${filter}`;
    });
});
</script>
{% endblock %}
