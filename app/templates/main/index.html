{% extends "base.html" %}

{% block content %}
<!-- Hero Section -->
<div class="jumbotron bg-primary text-white rounded p-5 mb-5">
    <div class="container text-center">
        <h1 class="display-4"><i class="fas fa-gamepad me-3"></i>Game Stats Tracker</h1>
        <p class="lead">Track your gaming performance across multiple games including Valorant, BGMI, and CS2</p>
        {% if not current_user.is_authenticated %}
        <div class="mt-4">
            <a href="{{ url_for('auth.register') }}" class="btn btn-light btn-lg me-3">
                <i class="fas fa-user-plus me-2"></i>Get Started
            </a>
            <a href="{{ url_for('auth.login') }}" class="btn btn-outline-light btn-lg">
                <i class="fas fa-sign-in-alt me-2"></i>Sign In
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Features Section -->
<div class="row mb-5">
    <div class="col-md-4 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
                <h5 class="card-title">Real-time Stats</h5>
                <p class="card-text">Automatically fetch and display your latest match statistics and performance metrics.</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <i class="fas fa-trophy fa-3x text-warning mb-3"></i>
                <h5 class="card-title">Achievements</h5>
                <p class="card-text">Unlock badges and achievements based on your gaming milestones and performance.</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <i class="fas fa-users fa-3x text-success mb-3"></i>
                <h5 class="card-title">Social Features</h5>
                <p class="card-text">Compare your stats with friends and compete with top players in your region.</p>
            </div>
        </div>
    </div>
</div>

<!-- Supported Games -->
{% if featured_games %}
<div class="row mb-5">
    <div class="col-12">
        <h2 class="text-center mb-4">Supported Games</h2>
        <div class="row">
            {% for game in featured_games %}
            <div class="col-md-4 mb-3">
                <div class="card">
                    {% if game.banner_url %}
                    <img src="{{ game.banner_url }}" class="card-img-top" alt="{{ game.name }}">
                    {% endif %}
                    <div class="card-body">
                        <h5 class="card-title">
                            {% if game.icon_url %}
                            <img src="{{ game.icon_url }}" alt="{{ game.name }}" class="me-2" style="width: 24px; height: 24px;">
                            {% endif %}
                            {{ game.name }}
                        </h5>
                        <p class="card-text">{{ game.description }}</p>
                        <small class="text-muted">{{ game.developer }}</small>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- Call to Action -->
{% if not current_user.is_authenticated %}
<div class="text-center bg-light p-5 rounded">
    <h3>Ready to Track Your Gaming Stats?</h3>
    <p class="lead">Join thousands of gamers who are already tracking their performance.</p>
    <a href="{{ url_for('auth.register') }}" class="btn btn-primary btn-lg">
        <i class="fas fa-rocket me-2"></i>Start Tracking Now
    </a>
</div>
{% endif %}
{% endblock %}
