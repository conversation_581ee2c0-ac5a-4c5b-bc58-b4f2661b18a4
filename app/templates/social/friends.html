{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-users me-2 text-primary"></i>Friends
    </h2>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFriendModal">
        <i class="fas fa-user-plus me-2"></i>Add Friend
    </button>
</div>

<!-- Friend Stats -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h3>{{ friends|length }}</h3>
                <p class="mb-0">Friends</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h3>{{ pending_requests|length }}</h3>
                <p class="mb-0">Pending Requests</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h3>{{ sent_requests|length }}</h3>
                <p class="mb-0">Sent Requests</p>
            </div>
        </div>
    </div>
</div>

<!-- Tabs -->
<ul class="nav nav-tabs mb-4" id="friendsTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="friends-tab" data-bs-toggle="tab" data-bs-target="#friends-content" type="button" role="tab">
            My Friends ({{ friends|length }})
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="requests-tab" data-bs-toggle="tab" data-bs-target="#requests-content" type="button" role="tab">
            Friend Requests ({{ pending_requests|length }})
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="sent-tab" data-bs-toggle="tab" data-bs-target="#sent-content" type="button" role="tab">
            Sent Requests ({{ sent_requests|length }})
        </button>
    </li>
</ul>

<div class="tab-content" id="friendsTabContent">
    <!-- Friends List -->
    <div class="tab-pane fade show active" id="friends-content" role="tabpanel">
        {% if friends %}
        <div class="row">
            {% for friendship in friends %}
            {% set friend = friendship.friend if friendship.user_id == current_user.id else friendship.user %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="avatar me-3">
                                <i class="fas fa-user-circle fa-3x text-muted"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h5 class="card-title mb-1">{{ friend.username }}</h5>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    Friends since {{ friendship.updated_at.strftime('%m/%d/%Y') }}
                                </small>
                            </div>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <a href="{{ url_for('social.compare_stats', friend_id=friend.id) }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-chart-bar me-1"></i>Compare
                            </a>
                            <button class="btn btn-sm btn-outline-danger" onclick="removeFriend({{ friend.id }}, '{{ friend.username }}')">
                                <i class="fas fa-user-minus me-1"></i>Remove
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No Friends Yet</h5>
            <p class="text-muted">Add friends to compare stats and compete on leaderboards!</p>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFriendModal">
                <i class="fas fa-user-plus me-2"></i>Add Your First Friend
            </button>
        </div>
        {% endif %}
    </div>
    
    <!-- Pending Requests -->
    <div class="tab-pane fade" id="requests-content" role="tabpanel">
        {% if pending_requests %}
        <div class="row">
            {% for friendship in pending_requests %}
            <div class="col-lg-6 mb-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <div class="avatar me-3">
                                    <i class="fas fa-user-circle fa-2x text-muted"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">{{ friendship.user.username }}</h6>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ friendship.created_at.strftime('%m/%d/%Y') }}
                                    </small>
                                    {% if friendship.message %}
                                    <p class="mb-0 mt-2 small text-muted">"{{ friendship.message }}"</p>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="d-flex gap-2">
                                <button class="btn btn-sm btn-success" onclick="respondToRequest({{ friendship.id }}, 'accept')">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="respondToRequest({{ friendship.id }}, 'decline')">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No Pending Requests</h5>
            <p class="text-muted">You don't have any pending friend requests.</p>
        </div>
        {% endif %}
    </div>
    
    <!-- Sent Requests -->
    <div class="tab-pane fade" id="sent-content" role="tabpanel">
        {% if sent_requests %}
        <div class="row">
            {% for friendship in sent_requests %}
            <div class="col-lg-6 mb-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <div class="avatar me-3">
                                    <i class="fas fa-user-circle fa-2x text-muted"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">{{ friendship.friend.username }}</h6>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        Sent {{ friendship.created_at.strftime('%m/%d/%Y') }}
                                    </small>
                                    {% if friendship.message %}
                                    <p class="mb-0 mt-2 small text-muted">"{{ friendship.message }}"</p>
                                    {% endif %}
                                </div>
                            </div>
                            <span class="badge bg-warning">Pending</span>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-paper-plane fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No Sent Requests</h5>
            <p class="text-muted">You haven't sent any friend requests yet.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Add Friend Modal -->
<div class="modal fade" id="addFriendModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Friend</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('social.add_friend') }}">
                {{ add_friend_form.hidden_tag() }}
                <div class="modal-body">
                    <div class="mb-3">
                        {{ add_friend_form.username_or_email.label(class="form-label") }}
                        {{ add_friend_form.username_or_email(class="form-control") }}
                    </div>
                    <div class="mb-3">
                        {{ add_friend_form.message.label(class="form-label") }}
                        {{ add_friend_form.message(class="form-control") }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    {{ add_friend_form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Hidden forms for actions -->
<form id="respondForm" method="POST" action="{{ url_for('social.respond_friend_request') }}" style="display: none;">
    {{ respond_form.hidden_tag() }}
    {{ respond_form.friendship_id() }}
    {{ respond_form.action() }}
    {{ respond_form.submit() }}
</form>

<form id="removeFriendForm" method="POST" action="{{ url_for('social.remove_friend') }}" style="display: none;">
    {{ csrf_token() }}
    <input type="hidden" name="friend_id" id="removeFriendId">
</form>

<script>
function respondToRequest(friendshipId, action) {
    document.getElementById('friendship_id').value = friendshipId;
    document.getElementById('action').value = action;
    document.getElementById('respondForm').submit();
}

function removeFriend(friendId, username) {
    if (confirm(`Are you sure you want to remove ${username} from your friends?`)) {
        document.getElementById('removeFriendId').value = friendId;
        document.getElementById('removeFriendForm').submit();
    }
}
</script>

<style>
.avatar {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}
</style>
{% endblock %}
