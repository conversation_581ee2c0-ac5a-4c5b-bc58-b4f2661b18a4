{% extends "base.html" %}

{% block content %}
<div class="row">
    <!-- User Info Card -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-body text-center">
                <div class="avatar mb-3">
                    <i class="fas fa-user-circle fa-5x text-muted"></i>
                </div>
                <h4>{{ user.username }}</h4>
                {% if user.first_name or user.last_name %}
                <p class="text-muted">{{ user.first_name }} {{ user.last_name }}</p>
                {% endif %}
                {% if user.bio %}
                <p class="text-muted">{{ user.bio }}</p>
                {% endif %}
                <div class="d-grid gap-2">
                    <a href="{{ url_for('auth.edit_profile') }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Edit Profile
                    </a>
                    <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-key me-2"></i>Change Password
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Connected Accounts Section -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-link me-2"></i>Connected Accounts
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted mb-4">
                    Link your gaming accounts to automatically sync stats and enable enhanced features.
                </p>
                
                <!-- Steam Account -->
                <div class="d-flex align-items-center justify-content-between p-3 border rounded mb-3">
                    <div class="d-flex align-items-center">
                        <i class="fab fa-steam fa-2x text-primary me-3"></i>
                        <div>
                            <h6 class="mb-1">Steam</h6>
                            {% set steam_account = oauth_accounts|selectattr('provider', 'equalto', 'steam')|first %}
                            {% if steam_account %}
                            <small class="text-success">
                                <i class="fas fa-check-circle me-1"></i>
                                Connected as {{ steam_account.display_name }}
                            </small>
                            {% else %}
                            <small class="text-muted">Connect your Steam account for CS2 stats</small>
                            {% endif %}
                        </div>
                    </div>
                    <div>
                        {% if steam_account %}
                        <form method="POST" action="{{ url_for('auth.oauth_unlink', provider='steam') }}" class="d-inline">
                            {{ csrf_token() }}
                            <button type="submit" class="btn btn-sm btn-outline-danger" 
                                    onclick="return confirm('Are you sure you want to unlink your Steam account?')">
                                <i class="fas fa-unlink me-1"></i>Unlink
                            </button>
                        </form>
                        {% else %}
                        <a href="{{ url_for('auth.oauth_login', provider='steam') }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-link me-1"></i>Connect
                        </a>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Riot Account -->
                <div class="d-flex align-items-center justify-content-between p-3 border rounded mb-3">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-gamepad fa-2x text-danger me-3"></i>
                        <div>
                            <h6 class="mb-1">Riot Games</h6>
                            {% set riot_account = oauth_accounts|selectattr('provider', 'equalto', 'riot')|first %}
                            {% if riot_account %}
                            <small class="text-success">
                                <i class="fas fa-check-circle me-1"></i>
                                Connected as {{ riot_account.display_name }}
                            </small>
                            {% else %}
                            <small class="text-muted">Connect your Riot account for Valorant stats</small>
                            {% endif %}
                        </div>
                    </div>
                    <div>
                        {% if riot_account %}
                        <form method="POST" action="{{ url_for('auth.oauth_unlink', provider='riot') }}" class="d-inline">
                            {{ csrf_token() }}
                            <button type="submit" class="btn btn-sm btn-outline-danger" 
                                    onclick="return confirm('Are you sure you want to unlink your Riot account?')">
                                <i class="fas fa-unlink me-1"></i>Unlink
                            </button>
                        </form>
                        {% else %}
                        <a href="{{ url_for('auth.oauth_login', provider='riot') }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-link me-1"></i>Connect
                        </a>
                        {% endif %}
                    </div>
                </div>
                
                <div class="alert alert-info mt-4">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Benefits of connecting accounts:</strong>
                    <ul class="mb-0 mt-2">
                        <li>Automatic stats synchronization</li>
                        <li>Enhanced profile information</li>
                        <li>Access to additional game features</li>
                        <li>Improved friend discovery</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Account Statistics -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Account Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-primary">{{ user.player_profiles|length }}</h4>
                            <small class="text-muted">Game Profiles</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-success">{{ oauth_accounts|length }}</h4>
                            <small class="text-muted">Connected Accounts</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-info">{{ user.sent_friend_requests|selectattr('status', 'equalto', 'accepted')|list|length }}</h4>
                            <small class="text-muted">Friends</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-warning">{{ user.user_achievements|length }}</h4>
                            <small class="text-muted">Achievements</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Activity -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>Recent Activity
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center py-4">
                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">Activity tracking coming soon!</h6>
                    <p class="text-muted">We'll show your recent matches, achievements, and social activity here.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar {
    width: 100px;
    height: 100px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.border.rounded.p-3:hover {
    background-color: #f8f9fa;
}
</style>
{% endblock %}
