"""
API routes for AJAX requests and external integrations
"""
from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from app import db
from app.models.user import User
from app.models.player_profile import PlayerProfile
from app.models.match import Match
from app.models.friendship import Friendship

api_bp = Blueprint('api', __name__)


@api_bp.route('/user/search')
@login_required
def search_users():
    """Search for users by username"""
    query = request.args.get('q', '').strip()
    if len(query) < 2:
        return jsonify({'users': []})
    
    users = User.query.filter(
        User.username.ilike(f'%{query}%'),
        User.id != current_user.id,
        User.is_active == True
    ).limit(10).all()
    
    return jsonify({
        'users': [
            {
                'id': user.id,
                'username': user.username,
                'full_name': user.get_full_name(),
                'avatar_url': user.avatar_url
            }
            for user in users
        ]
    })


@api_bp.route('/friend/request', methods=['POST'])
@login_required
def send_friend_request():
    """Send a friend request"""
    data = request.get_json()
    friend_id = data.get('friend_id')
    
    if not friend_id or friend_id == current_user.id:
        return jsonify({'error': 'Invalid friend ID'}), 400
    
    friend = User.query.get(friend_id)
    if not friend:
        return jsonify({'error': 'User not found'}), 404
    
    friendship, created = Friendship.send_friend_request(current_user.id, friend_id)
    
    if not created:
        return jsonify({'error': 'Friend request already exists'}), 400
    
    db.session.commit()
    
    return jsonify({
        'message': f'Friend request sent to {friend.username}',
        'friendship_id': friendship.id
    })


@api_bp.route('/friend/accept/<int:friendship_id>', methods=['POST'])
@login_required
def accept_friend_request(friendship_id):
    """Accept a friend request"""
    friendship = Friendship.query.filter_by(
        id=friendship_id,
        friend_id=current_user.id,
        status='pending'
    ).first()
    
    if not friendship:
        return jsonify({'error': 'Friend request not found'}), 404
    
    friendship.accept()
    db.session.commit()
    
    return jsonify({
        'message': 'Friend request accepted',
        'friendship_id': friendship.id
    })


@api_bp.route('/friend/reject/<int:friendship_id>', methods=['POST'])
@login_required
def reject_friend_request(friendship_id):
    """Reject a friend request"""
    friendship = Friendship.query.filter_by(
        id=friendship_id,
        friend_id=current_user.id,
        status='pending'
    ).first()
    
    if not friendship:
        return jsonify({'error': 'Friend request not found'}), 404
    
    db.session.delete(friendship)
    db.session.commit()
    
    return jsonify({'message': 'Friend request rejected'})


@api_bp.route('/profile/<int:profile_id>/update', methods=['POST'])
@login_required
def update_profile_stats(profile_id):
    """Trigger manual stats update for a profile"""
    profile = PlayerProfile.query.filter_by(
        id=profile_id,
        user_id=current_user.id
    ).first()
    
    if not profile:
        return jsonify({'error': 'Profile not found'}), 404
    
    # This would trigger a Celery task to update stats
    # For now, just return success
    return jsonify({
        'message': 'Stats update requested',
        'profile_id': profile.id
    })


@api_bp.route('/matches/recent')
@login_required
def get_recent_matches():
    """Get recent matches for dashboard"""
    limit = request.args.get('limit', 10, type=int)
    
    # Get all user's profiles
    profile_ids = [p.id for p in current_user.player_profiles.all()]
    
    if not profile_ids:
        return jsonify({'matches': []})
    
    matches = Match.query.filter(Match.player_profile_id.in_(profile_ids))\
                        .order_by(Match.match_date.desc())\
                        .limit(limit).all()
    
    return jsonify({
        'matches': [match.to_dict() for match in matches]
    })


@api_bp.route('/stats/summary')
@login_required
def get_stats_summary():
    """Get overall stats summary for the user"""
    profiles = current_user.player_profiles.all()
    
    total_matches = sum(p.total_matches for p in profiles)
    total_wins = sum(p.total_wins for p in profiles)
    total_kills = sum(p.total_kills for p in profiles)
    total_deaths = sum(p.total_deaths for p in profiles)
    
    overall_win_rate = (total_wins / total_matches * 100) if total_matches > 0 else 0
    overall_kd = (total_kills / total_deaths) if total_deaths > 0 else total_kills
    
    return jsonify({
        'total_matches': total_matches,
        'total_wins': total_wins,
        'win_rate': round(overall_win_rate, 2),
        'kd_ratio': round(overall_kd, 2),
        'active_profiles': len(profiles)
    })
