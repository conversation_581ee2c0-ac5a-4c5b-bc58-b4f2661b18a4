"""
Main application routes
"""
from flask import Blueprint, render_template, redirect, url_for
from flask_login import current_user
from app.models.game import Game

main_bp = Blueprint('main', __name__)


@main_bp.route('/')
def index():
    """Home page"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    
    # Get featured games for display
    featured_games = Game.get_featured_games()
    
    return render_template('main/index.html', 
                         title='Game Stats Tracker', 
                         featured_games=featured_games)


@main_bp.route('/about')
def about():
    """About page"""
    return render_template('main/about.html', title='About')


@main_bp.route('/games')
def games():
    """Games listing page"""
    active_games = Game.get_active_games()
    return render_template('main/games.html', 
                         title='Supported Games', 
                         games=active_games)


@main_bp.route('/privacy')
def privacy():
    """Privacy policy page"""
    return render_template('main/privacy.html', title='Privacy Policy')


@main_bp.route('/terms')
def terms():
    """Terms of service page"""
    return render_template('main/terms.html', title='Terms of Service')
