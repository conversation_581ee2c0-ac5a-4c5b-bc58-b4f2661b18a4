"""
Authentication routes
"""
from datetime import datetime
from flask import Blueprint, render_template, redirect, url_for, flash, request, session
from flask_login import login_user, logout_user, current_user, login_required
from app import db
from app.models.user import User
from app.models.oauth_account import OAuthAccount
from app.forms.auth import LoginForm, RegistrationForm, ProfileForm, ChangePasswordForm
from app.services.oauth_service import get_oauth_service, unlink_oauth_account
import logging

logger = logging.getLogger(__name__)

auth_bp = Blueprint('auth', __name__)


@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """User login"""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    form = LoginForm()
    if form.validate_on_submit():
        # Try to find user by username or email
        user = User.query.filter(
            (User.username == form.username.data) | 
            (User.email == form.username.data)
        ).first()
        
        if user is None or not user.check_password(form.password.data):
            flash('Invalid username/email or password', 'error')
            return redirect(url_for('auth.login'))
        
        if not user.is_active:
            flash('Your account has been deactivated. Please contact support.', 'error')
            return redirect(url_for('auth.login'))
        
        # Update last login time
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        login_user(user, remember=form.remember_me.data)
        
        # Redirect to next page or dashboard
        next_page = request.args.get('next')
        if not next_page or not next_page.startswith('/'):
            next_page = url_for('dashboard.index')
        
        flash(f'Welcome back, {user.get_full_name()}!', 'success')
        return redirect(next_page)
    
    return render_template('auth/login.html', title='Sign In', form=form)


@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """User registration"""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data.lower(),
            first_name=form.first_name.data,
            last_name=form.last_name.data
        )
        user.set_password(form.password.data)
        
        db.session.add(user)
        db.session.commit()
        
        flash('Congratulations, you are now registered!', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/register.html', title='Register', form=form)


@auth_bp.route('/logout')
@login_required
def logout():
    """User logout"""
    logout_user()
    flash('You have been logged out.', 'info')
    return redirect(url_for('main.index'))


@auth_bp.route('/profile')
@login_required
def profile():
    """User profile page"""
    # Get user's OAuth accounts
    oauth_accounts = OAuthAccount.get_user_accounts(current_user.id)
    return render_template('auth/profile.html', title='Profile', user=current_user, oauth_accounts=oauth_accounts)


@auth_bp.route('/edit_profile', methods=['GET', 'POST'])
@login_required
def edit_profile():
    """Edit user profile"""
    form = ProfileForm()
    
    if form.validate_on_submit():
        current_user.first_name = form.first_name.data
        current_user.last_name = form.last_name.data
        current_user.bio = form.bio.data
        current_user.updated_at = datetime.utcnow()
        
        db.session.commit()
        flash('Your profile has been updated.', 'success')
        return redirect(url_for('auth.profile'))
    
    elif request.method == 'GET':
        # Pre-populate form with current data
        form.first_name.data = current_user.first_name
        form.last_name.data = current_user.last_name
        form.bio.data = current_user.bio
    
    return render_template('auth/edit_profile.html', title='Edit Profile', form=form)


@auth_bp.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Change user password"""
    form = ChangePasswordForm()
    
    if form.validate_on_submit():
        if not current_user.check_password(form.current_password.data):
            flash('Current password is incorrect.', 'error')
            return redirect(url_for('auth.change_password'))
        
        current_user.set_password(form.new_password.data)
        current_user.updated_at = datetime.utcnow()
        
        db.session.commit()
        flash('Your password has been changed.', 'success')
        return redirect(url_for('auth.profile'))
    
    return render_template('auth/change_password.html', title='Change Password', form=form)


@auth_bp.route('/oauth/<provider>')
@login_required
def oauth_login(provider):
    """Initiate OAuth login for provider"""
    try:
        oauth_service = get_oauth_service(provider)
        auth_url = oauth_service.get_authorization_url()
        return redirect(auth_url)
    except ValueError as e:
        flash(f'Unsupported provider: {provider}', 'error')
        return redirect(url_for('auth.profile'))
    except Exception as e:
        logger.error(f"Error initiating OAuth for {provider}: {e}")
        flash('Error connecting to external service', 'error')
        return redirect(url_for('auth.profile'))


@auth_bp.route('/oauth/<provider>/callback')
@login_required
def oauth_callback(provider):
    """Handle OAuth callback from provider"""
    try:
        oauth_service = get_oauth_service(provider)

        if provider == 'steam':
            # Handle Steam OpenID response
            success, steam_id = oauth_service.verify_openid_response(request.args)
            if not success:
                flash('Steam authentication failed', 'error')
                return redirect(url_for('auth.profile'))

            # Get user info
            user_info = oauth_service.get_user_info(steam_id)
            if not user_info:
                flash('Failed to get Steam user information', 'error')
                return redirect(url_for('auth.profile'))

            # Link account
            success, message = oauth_service.link_account(
                current_user.id, steam_id, None, None, user_info
            )

        elif provider == 'riot':
            # Handle Riot OAuth response
            code = request.args.get('code')
            state = request.args.get('state')
            error = request.args.get('error')

            if error:
                flash(f'Riot authentication error: {error}', 'error')
                return redirect(url_for('auth.profile'))

            if not code:
                flash('No authorization code received', 'error')
                return redirect(url_for('auth.profile'))

            # Exchange code for token
            token_data = oauth_service.exchange_code_for_token(code, state)
            if not token_data:
                flash('Failed to get access token', 'error')
                return redirect(url_for('auth.profile'))

            # Get user info
            user_info = oauth_service.get_user_info(token_data.get('access_token'))
            if not user_info:
                flash('Failed to get Riot user information', 'error')
                return redirect(url_for('auth.profile'))

            # Link account
            success, message = oauth_service.link_account(
                current_user.id,
                user_info.get('sub'),  # Riot user ID
                token_data.get('access_token'),
                token_data.get('refresh_token'),
                user_info
            )

        else:
            flash(f'Unsupported provider: {provider}', 'error')
            return redirect(url_for('auth.profile'))

        # Clear OAuth state
        session.pop('oauth_state', None)

        if success:
            flash(f'{provider.title()} account linked successfully!', 'success')
        else:
            flash(message, 'error')

        return redirect(url_for('auth.profile'))

    except Exception as e:
        logger.error(f"Error in OAuth callback for {provider}: {e}")
        flash('Error processing authentication', 'error')
        return redirect(url_for('auth.profile'))


@auth_bp.route('/oauth/<provider>/unlink', methods=['POST'])
@login_required
def oauth_unlink(provider):
    """Unlink OAuth account"""
    try:
        success, message = unlink_oauth_account(current_user.id, provider)
        if success:
            flash(f'{provider.title()} account unlinked successfully!', 'success')
        else:
            flash(message, 'error')
    except Exception as e:
        logger.error(f"Error unlinking {provider} account: {e}")
        flash('Error unlinking account', 'error')

    return redirect(url_for('auth.profile'))
