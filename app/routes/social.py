"""
Social features routes (friends, comparisons, leaderboards)
"""
from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_required, current_user
from sqlalchemy import func, desc, and_, or_
from datetime import datetime, timedelta
from app import db
from app.models.user import User
from app.models.friendship import Friendship
from app.models.player_profile import PlayerProfile
from app.models.match import Match
from app.models.player_stats import PlayerStats
from app.models.game import Game
from app.forms.social import (AddFriendForm, RespondToFriendRequestForm, SearchPlayersForm,
                             ComparePlayersForm, RemoveFriendForm, BlockUserForm, LeaderboardFilterForm)
from app.utils.cache import cached
import logging

logger = logging.getLogger(__name__)

social_bp = Blueprint('social', __name__, url_prefix='/social')


@social_bp.route('/friends')
@login_required
def friends():
    """Friends list and management"""
    # Get user's friends
    friends = Friendship.get_friends(current_user.id)
    
    # Get pending friend requests (received)
    pending_requests = Friendship.get_pending_requests(current_user.id)
    
    # Get sent friend requests
    sent_requests = Friendship.get_sent_requests(current_user.id)
    
    # Forms
    add_friend_form = AddFriendForm()
    respond_form = RespondToFriendRequestForm()
    
    return render_template('social/friends.html',
                         title='Friends',
                         friends=friends,
                         pending_requests=pending_requests,
                         sent_requests=sent_requests,
                         add_friend_form=add_friend_form,
                         respond_form=respond_form)


@social_bp.route('/add_friend', methods=['POST'])
@login_required
def add_friend():
    """Send a friend request"""
    form = AddFriendForm()
    
    if form.validate_on_submit():
        username_or_email = form.username_or_email.data.strip()
        message = form.message.data.strip() if form.message.data else None
        
        # Find user by username or email
        user = User.query.filter(
            or_(User.username == username_or_email, User.email == username_or_email)
        ).first()
        
        if not user:
            flash('User not found', 'error')
            return redirect(url_for('social.friends'))
        
        if user.id == current_user.id:
            flash('You cannot add yourself as a friend', 'error')
            return redirect(url_for('social.friends'))
        
        # Check if friendship already exists
        existing = Friendship.get_friendship(current_user.id, user.id)
        if existing:
            if existing.status == 'accepted':
                flash('You are already friends with this user', 'info')
            elif existing.status == 'pending':
                flash('Friend request already sent', 'info')
            elif existing.status == 'blocked':
                flash('Cannot send friend request to this user', 'error')
        else:
            # Send friend request
            friendship = Friendship(
                user_id=current_user.id,
                friend_id=user.id,
                message=message,
                status='pending'
            )
            
            try:
                db.session.add(friendship)
                db.session.commit()
                flash(f'Friend request sent to {user.username}', 'success')
            except Exception as e:
                db.session.rollback()
                logger.error(f"Error sending friend request: {e}")
                flash('Error sending friend request', 'error')
    
    return redirect(url_for('social.friends'))


@social_bp.route('/respond_friend_request', methods=['POST'])
@login_required
def respond_friend_request():
    """Accept or decline a friend request"""
    form = RespondToFriendRequestForm()
    
    if form.validate_on_submit():
        friendship_id = int(form.friendship_id.data)
        action = form.action.data
        
        friendship = Friendship.query.get(friendship_id)
        if not friendship:
            flash('Friend request not found', 'error')
            return redirect(url_for('social.friends'))
        
        if friendship.friend_id != current_user.id:
            flash('Not authorized', 'error')
            return redirect(url_for('social.friends'))
        
        if friendship.status != 'pending':
            flash('Friend request is no longer pending', 'error')
            return redirect(url_for('social.friends'))
        
        try:
            if action == 'accept':
                friendship.accept()
                db.session.commit()
                flash(f'You are now friends with {friendship.user.username}', 'success')
            elif action == 'decline':
                db.session.delete(friendship)
                db.session.commit()
                flash('Friend request declined', 'info')
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error responding to friend request: {e}")
            flash('Error processing friend request', 'error')
    
    return redirect(url_for('social.friends'))


@social_bp.route('/remove_friend', methods=['POST'])
@login_required
def remove_friend():
    """Remove a friend"""
    form = RemoveFriendForm()
    
    if form.validate_on_submit():
        friend_id = int(form.friend_id.data)
        
        success, message = Friendship.remove_friend(current_user.id, friend_id)
        if success:
            flash(message, 'success')
        else:
            flash(message, 'error')
    
    return redirect(url_for('social.friends'))


@social_bp.route('/search')
@login_required
def search_players():
    """Search for players"""
    form = SearchPlayersForm()
    results = []
    
    if request.args.get('search_term'):
        search_term = request.args.get('search_term').strip()
        if len(search_term) >= 2:
            # Search users by username
            results = User.query.filter(
                User.username.ilike(f'%{search_term}%'),
                User.id != current_user.id,
                User.is_active == True
            ).limit(20).all()
            
            # Add friendship status to results
            for user in results:
                friendship = Friendship.get_friendship(current_user.id, user.id)
                if friendship:
                    user.friendship_status = friendship.status
                else:
                    user.friendship_status = None
    
    return render_template('social/search.html',
                         title='Search Players',
                         form=form,
                         results=results,
                         search_term=request.args.get('search_term', ''))


@social_bp.route('/compare')
@login_required
def compare_stats():
    """Compare stats with friends"""
    form = ComparePlayersForm(user_id=current_user.id)
    comparison_data = None
    
    if request.args.get('friend_id'):
        friend_id = int(request.args.get('friend_id'))
        game_id = request.args.get('game_id')
        time_period = request.args.get('time_period', 'all')
        
        # Verify friendship
        if not Friendship.are_friends(current_user.id, friend_id):
            flash('You can only compare stats with friends', 'error')
            return redirect(url_for('social.compare_stats'))
        
        friend = User.query.get(friend_id)
        if friend:
            comparison_data = get_comparison_data(current_user, friend, game_id, time_period)
    
    return render_template('social/compare.html',
                         title='Compare Stats',
                         form=form,
                         comparison_data=comparison_data)


@social_bp.route('/leaderboard')
@login_required
def leaderboard():
    """Global and friends leaderboard"""
    form = LeaderboardFilterForm()
    
    # Get filter parameters
    game_id = request.args.get('game_id', type=int)
    metric = request.args.get('metric', 'kd_ratio')
    time_period = request.args.get('time_period', 'all')
    friends_only = request.args.get('friends_only', 'all')
    
    # Build leaderboard query
    leaderboard_data = get_leaderboard_data(
        current_user.id, game_id, metric, time_period, friends_only
    )
    
    return render_template('social/leaderboard.html',
                         title='Leaderboard',
                         form=form,
                         leaderboard_data=leaderboard_data,
                         current_filters={
                             'game_id': game_id,
                             'metric': metric,
                             'time_period': time_period,
                             'friends_only': friends_only
                         })


@cached(timeout=300, key_prefix='comparison_data')
def get_comparison_data(user1, user2, game_id=None, time_period='all'):
    """Get comparison data between two users"""
    try:
        # Get time filter
        cutoff_date = None
        if time_period == '7':
            cutoff_date = datetime.utcnow() - timedelta(days=7)
        elif time_period == '30':
            cutoff_date = datetime.utcnow() - timedelta(days=30)
        
        # Get profiles for both users
        user1_profiles = PlayerProfile.query.filter_by(user_id=user1.id)
        user2_profiles = PlayerProfile.query.filter_by(user_id=user2.id)
        
        if game_id:
            user1_profiles = user1_profiles.filter_by(game_id=game_id)
            user2_profiles = user2_profiles.filter_by(game_id=game_id)
        
        user1_profiles = user1_profiles.all()
        user2_profiles = user2_profiles.all()
        
        # Calculate stats for each user
        user1_stats = calculate_user_stats(user1_profiles, cutoff_date)
        user2_stats = calculate_user_stats(user2_profiles, cutoff_date)
        
        return {
            'user1': {'user': user1, 'stats': user1_stats},
            'user2': {'user': user2, 'stats': user2_stats},
            'game_id': game_id,
            'time_period': time_period
        }
        
    except Exception as e:
        logger.error(f"Error getting comparison data: {e}")
        return None


def calculate_user_stats(profiles, cutoff_date=None):
    """Calculate aggregated stats for user profiles"""
    if not profiles:
        return {
            'total_matches': 0,
            'wins': 0,
            'losses': 0,
            'win_rate': 0,
            'avg_kd_ratio': 0,
            'total_kills': 0,
            'total_deaths': 0,
            'avg_score': 0
        }
    
    profile_ids = [p.id for p in profiles]
    
    # Base query for matches
    matches_query = Match.query.filter(Match.player_profile_id.in_(profile_ids))
    if cutoff_date:
        matches_query = matches_query.filter(Match.match_date >= cutoff_date)
    
    # Get match statistics
    total_matches = matches_query.count()
    wins = matches_query.filter_by(result='win').count()
    losses = matches_query.filter_by(result='loss').count()
    
    # Get player stats
    stats_query = db.session.query(
        func.avg(PlayerStats.kd_ratio).label('avg_kd'),
        func.sum(PlayerStats.kills).label('total_kills'),
        func.sum(PlayerStats.deaths).label('total_deaths'),
        func.avg(PlayerStats.score).label('avg_score')
    ).join(Match).filter(Match.player_profile_id.in_(profile_ids))
    
    if cutoff_date:
        stats_query = stats_query.filter(Match.match_date >= cutoff_date)
    
    stats_result = stats_query.first()
    
    return {
        'total_matches': total_matches,
        'wins': wins,
        'losses': losses,
        'win_rate': (wins / total_matches * 100) if total_matches > 0 else 0,
        'avg_kd_ratio': float(stats_result.avg_kd or 0),
        'total_kills': int(stats_result.total_kills or 0),
        'total_deaths': int(stats_result.total_deaths or 0),
        'avg_score': float(stats_result.avg_score or 0)
    }


@cached(timeout=600, key_prefix='leaderboard_data')
def get_leaderboard_data(user_id, game_id=None, metric='kd_ratio', time_period='all', friends_only='all'):
    """Get leaderboard data"""
    try:
        # Get time filter
        cutoff_date = None
        if time_period == '7':
            cutoff_date = datetime.utcnow() - timedelta(days=7)
        elif time_period == '30':
            cutoff_date = datetime.utcnow() - timedelta(days=30)
        
        # Base query
        if friends_only == 'friends':
            # Get friend IDs
            friends = Friendship.get_friends(user_id)
            friend_ids = [f.user_id if f.friend_id == user_id else f.friend_id for f in friends]
            friend_ids.append(user_id)  # Include current user
            
            profiles_query = PlayerProfile.query.filter(PlayerProfile.user_id.in_(friend_ids))
        else:
            profiles_query = PlayerProfile.query
        
        if game_id:
            profiles_query = profiles_query.filter_by(game_id=game_id)
        
        profiles = profiles_query.all()
        
        # Calculate stats for each user
        user_stats = {}
        for profile in profiles:
            if profile.user_id not in user_stats:
                user_stats[profile.user_id] = {
                    'user': profile.user,
                    'profiles': [],
                    'stats': None
                }
            user_stats[profile.user_id]['profiles'].append(profile)
        
        # Calculate aggregated stats
        leaderboard = []
        for user_data in user_stats.values():
            stats = calculate_user_stats(user_data['profiles'], cutoff_date)
            if stats['total_matches'] > 0:  # Only include users with matches
                leaderboard.append({
                    'user': user_data['user'],
                    'stats': stats,
                    'metric_value': stats.get(metric, 0)
                })
        
        # Sort by metric
        leaderboard.sort(key=lambda x: x['metric_value'], reverse=True)
        
        return leaderboard[:50]  # Top 50
        
    except Exception as e:
        logger.error(f"Error getting leaderboard data: {e}")
        return []
