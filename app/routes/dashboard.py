"""
Dashboard routes for authenticated users
"""
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, session
from flask_login import login_required, current_user
from app import db
from app.models.game import Game
from app.models.player_profile import PlayerProfile
from app.models.match import Match
from app.models.player_stats import PlayerStats
from app.models.achievement import Achievement, UserAchievement
from app.models.user import User
from app.models.friendship import Friendship
from app.forms.profile import AddProfileForm, EditProfileForm, UpdateStatsForm, DeleteProfileForm, SearchPlayerForm
from app.services import get_service_for_game, APIException, PlayerNotFoundException
from app.tasks.stats_tasks import update_profile_stats, bulk_update_profiles_stats
from app.tasks.match_tasks import fetch_recent_matches
from app.utils.cache import cached, invalidate_user_cache, invalidate_profile_cache
from app.services.achievement_service import AchievementService
from datetime import datetime, timedelta
import logging

dashboard_bp = Blueprint('dashboard', __name__)


@dashboard_bp.route('/')
@login_required
def index():
    """Main dashboard"""
    # Get user's player profiles
    profiles = current_user.player_profiles.filter_by(is_public=True).all()
    
    # Get recent matches across all profiles
    recent_matches = []
    for profile in profiles:
        matches = profile.get_recent_matches(5)
        recent_matches.extend(matches)
    
    # Sort by date and limit
    recent_matches.sort(key=lambda x: x.match_date, reverse=True)
    recent_matches = recent_matches[:10]
    
    # Get recent achievements
    recent_achievements = UserAchievement.query.filter_by(user_id=current_user.id)\
                                              .order_by(UserAchievement.unlocked_at.desc())\
                                              .limit(5).all()
    
    # Calculate overall stats
    total_matches = sum(profile.total_matches for profile in profiles)
    total_wins = sum(profile.total_wins for profile in profiles)
    overall_win_rate = (total_wins / total_matches * 100) if total_matches > 0 else 0
    
    return render_template('dashboard/index.html',
                         title='Dashboard',
                         profiles=profiles,
                         recent_matches=recent_matches,
                         recent_achievements=recent_achievements,
                         total_matches=total_matches,
                         total_wins=total_wins,
                         overall_win_rate=overall_win_rate)


@dashboard_bp.route('/profiles')
@login_required
def profiles():
    """User's game profiles"""
    user_profiles = current_user.player_profiles.all()
    available_games = Game.get_active_games()
    
    return render_template('dashboard/profiles.html',
                         title='My Game Profiles',
                         profiles=user_profiles,
                         available_games=available_games)


@dashboard_bp.route('/profile/<int:profile_id>')
@login_required
def profile_detail(profile_id):
    """Detailed view of a specific profile"""
    profile = PlayerProfile.query.filter_by(
        id=profile_id,
        user_id=current_user.id
    ).first_or_404()
    
    # Get recent matches
    recent_matches = profile.get_recent_matches(20)
    
    # Get profile stats
    stats = {
        'total_matches': profile.total_matches,
        'wins': profile.total_wins,
        'losses': profile.total_losses,
        'win_rate': profile.win_rate,
        'kd_ratio': profile.kd_ratio,
        'current_rank': profile.get_rank_display()
    }
    
    return render_template('dashboard/profile_detail.html',
                         title=f'{profile.get_full_player_name()} - {profile.game.name}',
                         profile=profile,
                         matches=recent_matches,
                         stats=stats)


@dashboard_bp.route('/matches')
@login_required
def matches():
    """All user matches"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # Get all matches for user's profiles
    profile_ids = [p.id for p in current_user.player_profiles.all()]
    
    matches_query = Match.query.filter(Match.player_profile_id.in_(profile_ids))\
                              .order_by(Match.match_date.desc())
    
    matches_pagination = matches_query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('dashboard/matches.html',
                         title='Match History',
                         matches=matches_pagination.items,
                         pagination=matches_pagination)


@dashboard_bp.route('/achievements')
@login_required
def achievements():
    """User achievements dashboard"""
    try:
        # Initialize achievements if not already done
        AchievementService.initialize_achievements()

        # Check for new achievements
        newly_earned = AchievementService.check_user_achievements(current_user.id)
        if newly_earned:
            for achievement in newly_earned:
                flash(f'🎉 Achievement Unlocked: {achievement.name}!', 'success')

        # Get all achievements
        all_achievements = Achievement.query.all()

        # Get user's earned achievements
        earned_achievements = UserAchievement.query.filter_by(user_id=current_user.id)\
                                                  .join(Achievement)\
                                                  .order_by(UserAchievement.earned_at.desc())\
                                                  .all()

        # Create lookup dictionaries
        earned_achievement_ids = {ua.achievement_id for ua in earned_achievements}
        user_achievements = {ua.achievement_id: ua for ua in earned_achievements}

        # Calculate stats
        earned_count = len(earned_achievements)
        total_achievements = len(all_achievements)
        total_points = sum(ua.achievement.points for ua in earned_achievements)
        completion_percentage = (earned_count / total_achievements * 100) if total_achievements > 0 else 0

        return render_template('dashboard/achievements.html',
                             title='My Achievements',
                             all_achievements=all_achievements,
                             earned_achievements=earned_achievements,
                             earned_achievement_ids=earned_achievement_ids,
                             user_achievements=user_achievements,
                             earned_count=earned_count,
                             total_achievements=total_achievements,
                             total_points=total_points,
                             completion_percentage=completion_percentage)

    except Exception as e:
        logging.error(f"Error loading achievements: {e}")
        flash('Error loading achievements', 'error')
        return redirect(url_for('dashboard.index'))


@dashboard_bp.route('/friends')
@login_required
def friends():
    """Friends list"""
    friends = current_user.get_friends()
    
    return render_template('dashboard/friends.html',
                         title='Friends',
                         friends=friends)


@dashboard_bp.route('/stats')
@login_required
def stats():
    """Detailed statistics page"""
    profiles = current_user.player_profiles.all()
    
    # Aggregate stats across all games
    stats_by_game = {}
    for profile in profiles:
        game_name = profile.game.name
        stats_by_game[game_name] = {
            'profile': profile,
            'matches': profile.total_matches,
            'wins': profile.total_wins,
            'losses': profile.total_losses,
            'win_rate': profile.win_rate,
            'kd_ratio': profile.kd_ratio,
            'rank': profile.get_rank_display()
        }
    
    return render_template('dashboard/stats.html',
                         title='Statistics',
                         stats_by_game=stats_by_game)


@dashboard_bp.route('/api/profile/<int:profile_id>/stats')
@login_required
def api_profile_stats(profile_id):
    """API endpoint for profile statistics (for charts)"""
    profile = PlayerProfile.query.filter_by(
        id=profile_id,
        user_id=current_user.id
    ).first_or_404()
    
    # Get recent matches for chart data
    recent_matches = profile.get_recent_matches(30)
    
    # Prepare data for charts
    match_dates = [m.match_date.strftime('%Y-%m-%d') for m in recent_matches]
    win_loss_data = [1 if m.result == 'win' else 0 for m in recent_matches]
    kd_ratios = [m.kd_ratio for m in recent_matches]
    
    return jsonify({
        'match_dates': match_dates,
        'win_loss_data': win_loss_data,
        'kd_ratios': kd_ratios,
        'total_matches': profile.total_matches,
        'win_rate': profile.win_rate,
        'kd_ratio': profile.kd_ratio
    })


@dashboard_bp.route('/profiles/add', methods=['GET', 'POST'])
@login_required
def add_profile():
    """Add a new game profile"""
    form = AddProfileForm()

    if form.validate_on_submit():
        try:
            # Get the selected game
            game = Game.query.get(form.game_id.data)
            if not game:
                flash('Invalid game selected', 'error')
                return render_template('dashboard/add_profile.html', form=form)

            # Verify player exists using API
            api_service = get_service_for_game(game.api_name)
            external_id = form.external_id.data

            if api_service:
                try:
                    if game.api_name == 'valorant':
                        # For Valorant, we need both name and tag
                        if not form.player_tag.data:
                            flash('Player tag is required for Valorant', 'error')
                            return render_template('dashboard/add_profile.html', form=form)

                        player_info = api_service.get_player_info(
                            form.player_name.data,
                            form.player_tag.data,
                            form.region.data
                        )
                        external_id = player_info.get('player_id')  # PUUID

                    elif game.api_name == 'cs2':
                        # For CS2, use Steam ID or player name
                        identifier = form.external_id.data or form.player_name.data
                        player_info = api_service.get_player_info(identifier)
                        external_id = player_info.get('player_id')  # Steam ID 64

                    else:
                        # For other games (like BGMI), just validate format
                        player_info = {'player_id': form.player_name.data}

                except (APIException, PlayerNotFoundException) as e:
                    flash(f'Could not verify player: {str(e)}', 'warning')
                    # Continue anyway - user might want to add profile before it's available in API

            # Create new profile
            profile = PlayerProfile(
                user_id=current_user.id,
                game_id=form.game_id.data,
                player_name=form.player_name.data,
                player_tag=form.player_tag.data,
                external_id=external_id,
                region=form.region.data,
                notes=form.notes.data,
                is_active=True,
                created_at=datetime.utcnow(),
                last_updated=datetime.utcnow()
            )

            db.session.add(profile)
            db.session.commit()

            flash(f'Successfully added {game.name} profile!', 'success')
            return redirect(url_for('dashboard.profiles'))

        except Exception as e:
            db.session.rollback()
            logging.error(f"Error adding profile: {e}")
            flash('An error occurred while adding the profile', 'error')

    return render_template('dashboard/add_profile.html', form=form)


@dashboard_bp.route('/profiles/<int:profile_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_profile(profile_id):
    """Edit an existing game profile"""
    profile = PlayerProfile.query.filter_by(
        id=profile_id,
        user_id=current_user.id
    ).first_or_404()

    form = EditProfileForm(profile=profile)

    if form.validate_on_submit():
        try:
            # Update profile fields
            profile.player_name = form.player_name.data
            profile.player_tag = form.player_tag.data
            profile.external_id = form.external_id.data
            profile.region = form.region.data
            profile.notes = form.notes.data
            profile.is_active = form.is_active.data
            profile.last_updated = datetime.utcnow()

            db.session.commit()

            flash('Profile updated successfully!', 'success')
            return redirect(url_for('dashboard.profiles'))

        except Exception as e:
            db.session.rollback()
            logging.error(f"Error updating profile: {e}")
            flash('An error occurred while updating the profile', 'error')

    return render_template('dashboard/edit_profile.html', form=form, profile=profile)


@dashboard_bp.route('/profiles/<int:profile_id>/update-stats', methods=['POST'])
@login_required
def update_profile_stats_route(profile_id):
    """Manually trigger stats update for a profile using background task"""
    profile = PlayerProfile.query.filter_by(
        id=profile_id,
        user_id=current_user.id
    ).first_or_404()

    try:
        # Invalidate cache before update
        invalidate_profile_cache(profile_id)
        invalidate_user_cache(current_user.id)

        # Queue the background task
        task = update_profile_stats.delay(profile_id)

        flash(f'Stats update queued for {profile.game.name} profile. This may take a few minutes.', 'info')

        # Store task ID in session for potential status checking
        if 'background_tasks' not in session:
            session['background_tasks'] = {}
        session['background_tasks'][f'stats_update_{profile_id}'] = task.id

    except Exception as e:
        logging.error(f"Error queuing stats update: {e}")
        flash(f'Error starting stats update: {str(e)}', 'error')

    return redirect(url_for('dashboard.profile_detail', profile_id=profile_id))


@dashboard_bp.route('/profiles/<int:profile_id>/delete', methods=['GET', 'POST'])
@login_required
def delete_profile(profile_id):
    """Delete a game profile"""
    profile = PlayerProfile.query.filter_by(
        id=profile_id,
        user_id=current_user.id
    ).first_or_404()

    form = DeleteProfileForm()

    if form.validate_on_submit():
        try:
            game_name = profile.game.name
            db.session.delete(profile)
            db.session.commit()

            flash(f'Successfully deleted {game_name} profile', 'success')
            return redirect(url_for('dashboard.profiles'))

        except Exception as e:
            db.session.rollback()
            logging.error(f"Error deleting profile: {e}")
            flash('An error occurred while deleting the profile', 'error')

    return render_template('dashboard/delete_profile.html', form=form, profile=profile)


@dashboard_bp.route('/profiles/update-all-stats', methods=['POST'])
@login_required
def update_all_profiles_stats():
    """Trigger stats update for all user's profiles"""
    try:
        # Get all user's active profiles
        profile_ids = [p.id for p in current_user.player_profiles if p.is_active]

        if not profile_ids:
            flash('No active profiles to update', 'warning')
            return redirect(url_for('dashboard.profiles'))

        # Queue the bulk update task
        task = bulk_update_profiles_stats.delay(profile_ids)

        flash(f'Stats update queued for {len(profile_ids)} profiles. This may take several minutes.', 'info')

        # Store task ID in session
        if 'background_tasks' not in session:
            session['background_tasks'] = {}
        session['background_tasks']['bulk_stats_update'] = task.id

    except Exception as e:
        logging.error(f"Error queuing bulk stats update: {e}")
        flash(f'Error starting bulk update: {str(e)}', 'error')

    return redirect(url_for('dashboard.profiles'))


@dashboard_bp.route('/profiles/<int:profile_id>/fetch-matches', methods=['POST'])
@login_required
def fetch_profile_matches(profile_id):
    """Fetch recent matches for a profile"""
    profile = PlayerProfile.query.filter_by(
        id=profile_id,
        user_id=current_user.id
    ).first_or_404()

    try:
        # Queue the match fetch task
        task = fetch_recent_matches.delay(profile_id, 20)  # Fetch up to 20 matches

        flash(f'Match fetch queued for {profile.game.name} profile. This may take a few minutes.', 'info')

        # Store task ID in session
        if 'background_tasks' not in session:
            session['background_tasks'] = {}
        session['background_tasks'][f'match_fetch_{profile_id}'] = task.id

    except Exception as e:
        logging.error(f"Error queuing match fetch: {e}")
        flash(f'Error starting match fetch: {str(e)}', 'error')

    return redirect(url_for('dashboard.profile_detail', profile_id=profile_id))


@dashboard_bp.route('/api/task-status/<task_id>')
@login_required
def task_status(task_id):
    """Check the status of a background task"""
    try:
        from app.celery_app import celery

        task = celery.AsyncResult(task_id)

        if task.state == 'PENDING':
            response = {
                'state': task.state,
                'status': 'Task is waiting to be processed...'
            }
        elif task.state == 'PROGRESS':
            response = {
                'state': task.state,
                'status': task.info.get('status', 'Processing...'),
                'current': task.info.get('current', 0),
                'total': task.info.get('total', 1)
            }
        elif task.state == 'SUCCESS':
            response = {
                'state': task.state,
                'status': 'Task completed successfully',
                'result': task.result
            }
        else:  # FAILURE
            response = {
                'state': task.state,
                'status': 'Task failed',
                'error': str(task.info)
            }

        return jsonify(response)

    except Exception as e:
        return jsonify({
            'state': 'ERROR',
            'status': 'Error checking task status',
            'error': str(e)
        })


@dashboard_bp.route('/stats')
@login_required
def stats():
    """Display comprehensive stats dashboard"""
    try:
        # Get filter parameter
        filter_days = request.args.get('filter', 'all')

        # Calculate date filter
        if filter_days != 'all':
            try:
                days = int(filter_days)
                cutoff_date = datetime.utcnow() - timedelta(days=days)
            except ValueError:
                cutoff_date = None
        else:
            cutoff_date = None

        # Get user's profiles
        profiles = current_user.player_profiles

        # Calculate overall stats
        total_matches = 0
        total_wins = 0
        total_kd_ratio = 0.0
        active_profiles = 0

        for profile in profiles:
            if profile.is_active:
                active_profiles += 1
                total_matches += profile.total_matches or 0
                total_wins += profile.wins or 0
                if profile.kd_ratio:
                    total_kd_ratio += profile.kd_ratio

        overall_win_rate = (total_wins / total_matches) if total_matches > 0 else 0
        overall_kd_ratio = (total_kd_ratio / active_profiles) if active_profiles > 0 else 0

        # Get recent matches with filter
        matches_query = Match.query.join(PlayerProfile).filter(
            PlayerProfile.user_id == current_user.id
        )

        if cutoff_date:
            matches_query = matches_query.filter(Match.match_date >= cutoff_date)

        recent_matches = matches_query.order_by(Match.match_date.desc()).limit(20).all()

        # Prepare chart data
        win_rate_data = prepare_win_rate_trend_data(current_user.id, cutoff_date)
        kd_data = prepare_kd_trend_data(current_user.id, cutoff_date)
        game_performance_data = prepare_game_performance_data(current_user.id, cutoff_date)
        match_results_data = prepare_match_results_data(current_user.id, cutoff_date)

        return render_template('dashboard/stats.html',
                             total_matches=total_matches,
                             overall_win_rate=overall_win_rate,
                             overall_kd_ratio=overall_kd_ratio,
                             active_profiles=active_profiles,
                             recent_matches=recent_matches,
                             win_rate_data=win_rate_data,
                             kd_data=kd_data,
                             game_performance_data=game_performance_data,
                             match_results_data=match_results_data)

    except Exception as e:
        logging.error(f"Error loading stats dashboard: {e}")
        flash('Error loading stats dashboard', 'error')
        return redirect(url_for('dashboard.index'))


@cached(timeout=300, key_prefix='stats:win_rate')
def prepare_win_rate_trend_data(user_id, cutoff_date=None):
    """Prepare win rate trend data for charts"""
    try:
        # Get matches grouped by date
        matches_query = db.session.query(
            db.func.date(Match.match_date).label('date'),
            db.func.count(Match.id).label('total'),
            db.func.sum(db.case((Match.result == 'win', 1), else_=0)).label('wins')
        ).join(PlayerProfile).filter(PlayerProfile.user_id == user_id)

        if cutoff_date:
            matches_query = matches_query.filter(Match.match_date >= cutoff_date)

        daily_stats = matches_query.group_by(db.func.date(Match.match_date)).order_by('date').all()

        labels = []
        data = []

        for stat in daily_stats[-30:]:  # Last 30 data points
            labels.append(stat.date.strftime('%m/%d'))
            win_rate = (stat.wins / stat.total * 100) if stat.total > 0 else 0
            data.append(round(win_rate, 1))

        return {'labels': labels, 'data': data}

    except Exception as e:
        logging.error(f"Error preparing win rate data: {e}")
        return {'labels': [], 'data': []}


@cached(timeout=300, key_prefix='stats:kd')
def prepare_kd_trend_data(user_id, cutoff_date=None):
    """Prepare K/D ratio trend data for charts"""
    try:
        # Get daily K/D ratios
        stats_query = db.session.query(
            db.func.date(Match.match_date).label('date'),
            db.func.avg(PlayerStats.kd_ratio).label('avg_kd')
        ).join(Match).join(PlayerProfile).filter(
            PlayerProfile.user_id == user_id,
            PlayerStats.kd_ratio.isnot(None)
        )

        if cutoff_date:
            stats_query = stats_query.filter(Match.match_date >= cutoff_date)

        daily_kd = stats_query.group_by(db.func.date(Match.match_date)).order_by('date').all()

        labels = []
        data = []

        for stat in daily_kd[-30:]:  # Last 30 data points
            labels.append(stat.date.strftime('%m/%d'))
            data.append(round(stat.avg_kd, 2) if stat.avg_kd else 0)

        return {'labels': labels, 'data': data}

    except Exception as e:
        logging.error(f"Error preparing K/D data: {e}")
        return {'labels': [], 'data': []}


@cached(timeout=300, key_prefix='stats:game_performance')
def prepare_game_performance_data(user_id, cutoff_date=None):
    """Prepare game performance comparison data"""
    try:
        # Get stats by game
        game_stats = db.session.query(
            Game.name,
            db.func.count(Match.id).label('total_matches'),
            db.func.sum(db.case((Match.result == 'win', 1), else_=0)).label('wins'),
            db.func.avg(PlayerStats.kd_ratio).label('avg_kd')
        ).join(PlayerProfile).join(Match).join(PlayerStats).filter(
            PlayerProfile.user_id == user_id
        )

        if cutoff_date:
            game_stats = game_stats.filter(Match.match_date >= cutoff_date)

        game_stats = game_stats.group_by(Game.name).all()

        labels = []
        win_rates = []
        kd_ratios = []

        for stat in game_stats:
            labels.append(stat.name)
            win_rate = (stat.wins / stat.total_matches * 100) if stat.total_matches > 0 else 0
            win_rates.append(round(win_rate, 1))
            kd_ratios.append(round(stat.avg_kd, 2) if stat.avg_kd else 0)

        return {
            'labels': labels,
            'winRates': win_rates,
            'kdRatios': kd_ratios
        }

    except Exception as e:
        logging.error(f"Error preparing game performance data: {e}")
        return {'labels': [], 'winRates': [], 'kdRatios': []}


@cached(timeout=300, key_prefix='stats:match_results')
def prepare_match_results_data(user_id, cutoff_date=None):
    """Prepare match results pie chart data"""
    try:
        # Get match results count
        results_query = db.session.query(
            Match.result,
            db.func.count(Match.id).label('count')
        ).join(PlayerProfile).filter(PlayerProfile.user_id == user_id)

        if cutoff_date:
            results_query = results_query.filter(Match.match_date >= cutoff_date)

        results = results_query.group_by(Match.result).all()

        labels = []
        data = []

        for result in results:
            labels.append(result.result.title())
            data.append(result.count)

        return {'labels': labels, 'data': data}

    except Exception as e:
        logging.error(f"Error preparing match results data: {e}")
        return {'labels': [], 'data': []}
