"""
Celery tasks for fetching and processing match data
"""
from app.celery_app import celery
from app import db
from app.models.player_profile import PlayerProfile
from app.models.match import Match
from app.models.player_stats import PlayerStats
from app.services import get_service_for_game, APIException, PlayerNotFoundException
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)


@celery.task(bind=True, max_retries=3)
def fetch_recent_matches(self, profile_id, limit=10):
    """
    Fetch recent matches for a single player profile
    
    Args:
        profile_id (int): ID of the PlayerProfile
        limit (int): Maximum number of matches to fetch
        
    Returns:
        dict: Results with match count and success status
    """
    try:
        profile = PlayerProfile.query.get(profile_id)
        if not profile:
            logger.error(f"Profile {profile_id} not found")
            return {'success': False, 'error': 'Profile not found'}
        
        if not profile.is_active:
            logger.info(f"Skipping inactive profile {profile_id}")
            return {'success': True, 'skipped': True, 'reason': 'Profile inactive'}
        
        # Get API service for the game
        api_service = get_service_for_game(profile.game.api_name)
        if not api_service:
            logger.error(f"No API service available for {profile.game.name}")
            return {'success': False, 'error': f'No API service for {profile.game.name}'}
        
        # Determine player identifier
        if profile.game.api_name == 'valorant':
            if not profile.player_tag:
                logger.error(f"Valorant profile {profile_id} missing player tag")
                return {'success': False, 'error': 'Missing player tag for Valorant'}
            identifier = f"{profile.player_name}#{profile.player_tag}"
        else:
            identifier = profile.external_id or profile.player_name
        
        # Fetch recent matches from API
        try:
            matches_data = api_service.get_recent_matches(identifier, region=profile.region, limit=limit)
        except PlayerNotFoundException:
            logger.warning(f"Player not found for profile {profile_id}: {identifier}")
            return {'success': False, 'error': 'Player not found in API'}
        except APIException as e:
            logger.error(f"API error for profile {profile_id}: {e}")
            # Retry with exponential backoff
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        
        # Process and store matches
        new_matches = 0
        for match_data in matches_data:
            # Check if match already exists
            existing_match = Match.query.filter_by(
                external_match_id=match_data.get('match_id'),
                player_profile_id=profile_id
            ).first()
            
            if existing_match:
                continue  # Skip existing matches
            
            # Create new match record
            match = Match(
                player_profile_id=profile_id,
                external_match_id=match_data.get('match_id'),
                match_date=datetime.fromisoformat(match_data.get('match_date')),
                game_mode=match_data.get('game_mode', 'Unknown'),
                map_name=match_data.get('map_name', 'Unknown'),
                result=match_data.get('result', 'unknown'),  # win/loss/draw
                duration_seconds=match_data.get('duration', 0),
                created_at=datetime.utcnow()
            )
            
            db.session.add(match)
            db.session.flush()  # Get the match ID
            
            # Create player stats for this match
            stats_data = match_data.get('player_stats', {})
            player_stats = PlayerStats(
                match_id=match.id,
                player_profile_id=profile_id,
                kills=stats_data.get('kills', 0),
                deaths=stats_data.get('deaths', 0),
                assists=stats_data.get('assists', 0),
                score=stats_data.get('score', 0),
                damage_dealt=stats_data.get('damage_dealt', 0),
                headshots=stats_data.get('headshots', 0),
                first_kills=stats_data.get('first_kills', 0),
                created_at=datetime.utcnow()
            )
            
            db.session.add(player_stats)
            new_matches += 1
        
        db.session.commit()
        
        logger.info(f"Fetched {new_matches} new matches for profile {profile_id}")
        
        return {
            'success': True,
            'profile_id': profile_id,
            'new_matches': new_matches,
            'total_fetched': len(matches_data)
        }
        
    except Exception as e:
        logger.error(f"Error fetching matches for profile {profile_id}: {e}")
        db.session.rollback()
        
        # Retry with exponential backoff for unexpected errors
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        
        return {'success': False, 'error': str(e)}


@celery.task(bind=True)
def fetch_recent_matches_for_all_profiles(self):
    """
    Fetch recent matches for all active player profiles
    
    Returns:
        dict: Summary of fetch results
    """
    try:
        # Get all active profiles
        profiles = PlayerProfile.query.filter_by(is_active=True).all()
        
        logger.info(f"Starting match fetch for {len(profiles)} profiles")
        
        results = {
            'total_profiles': len(profiles),
            'successful_fetches': 0,
            'failed_fetches': 0,
            'total_new_matches': 0,
            'errors': []
        }
        
        # Process profiles in batches to avoid overwhelming APIs
        batch_size = 5
        for i in range(0, len(profiles), batch_size):
            batch = profiles[i:i + batch_size]
            
            for profile in batch:
                try:
                    # Call the individual fetch task
                    result = fetch_recent_matches.apply_async(
                        args=[profile.id, 20],  # Fetch up to 20 recent matches
                        countdown=i // batch_size * 60  # Stagger requests by 1 minute per batch
                    ).get(timeout=600)  # 10 minute timeout
                    
                    if result.get('success'):
                        if not result.get('skipped'):
                            results['successful_fetches'] += 1
                            results['total_new_matches'] += result.get('new_matches', 0)
                    else:
                        results['failed_fetches'] += 1
                        results['errors'].append({
                            'profile_id': profile.id,
                            'error': result.get('error', 'Unknown error')
                        })
                        
                except Exception as e:
                    logger.error(f"Error processing profile {profile.id}: {e}")
                    results['failed_fetches'] += 1
                    results['errors'].append({
                        'profile_id': profile.id,
                        'error': str(e)
                    })
        
        logger.info(f"Match fetch completed: {results['successful_fetches']} successful, "
                   f"{results['failed_fetches']} failed, {results['total_new_matches']} new matches")
        
        return results
        
    except Exception as e:
        logger.error(f"Error in bulk match fetch: {e}")
        return {
            'success': False,
            'error': str(e)
        }


@celery.task(bind=True)
def process_match_data(self, match_id):
    """
    Process and analyze match data for insights
    
    Args:
        match_id (int): ID of the match to process
        
    Returns:
        dict: Processing results
    """
    try:
        match = Match.query.get(match_id)
        if not match:
            return {'success': False, 'error': 'Match not found'}
        
        # Get player stats for this match
        player_stats = PlayerStats.query.filter_by(match_id=match_id).first()
        if not player_stats:
            return {'success': False, 'error': 'Player stats not found'}
        
        # Calculate derived metrics
        if player_stats.deaths > 0:
            player_stats.kd_ratio = player_stats.kills / player_stats.deaths
        else:
            player_stats.kd_ratio = player_stats.kills  # No deaths = kills as KD
        
        if player_stats.kills > 0:
            player_stats.headshot_percentage = (player_stats.headshots / player_stats.kills) * 100
        else:
            player_stats.headshot_percentage = 0.0
        
        # Update match analysis
        match.analyzed = True
        match.analysis_date = datetime.utcnow()
        
        db.session.commit()
        
        logger.info(f"Processed match {match_id} with KD: {player_stats.kd_ratio:.2f}")
        
        return {
            'success': True,
            'match_id': match_id,
            'kd_ratio': player_stats.kd_ratio,
            'headshot_percentage': player_stats.headshot_percentage
        }
        
    except Exception as e:
        logger.error(f"Error processing match {match_id}: {e}")
        db.session.rollback()
        return {'success': False, 'error': str(e)}
