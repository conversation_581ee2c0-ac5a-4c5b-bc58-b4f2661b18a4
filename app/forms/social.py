"""
Forms for social features (friends, comparisons)
"""
from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, SelectField, SubmitField, TextAreaField, HiddenField
from wtforms.validators import DataRequired, Length, Email, Optional
from app.models.user import User
from app.models.game import Game


class AddFriendForm(FlaskForm):
    """Form for adding friends"""
    username_or_email = StringField('Username or Email', 
                                   validators=[DataRequired(), Length(min=3, max=100)],
                                   render_kw={'placeholder': 'Enter username or email'})
    message = TextAreaField('Message (Optional)', 
                           validators=[Optional(), Length(max=500)],
                           render_kw={'placeholder': 'Add a personal message...', 'rows': 3})
    submit = SubmitField('Send Friend Request')


class RespondToFriendRequestForm(FlaskForm):
    """Form for responding to friend requests"""
    friendship_id = HiddenField('Friendship ID', validators=[DataRequired()])
    action = HiddenField('Action', validators=[DataRequired()])
    submit = SubmitField('Respond')


class SearchPlayersForm(FlaskForm):
    """Form for searching players"""
    search_term = StringField('Search Players', 
                             validators=[DataRequired(), Length(min=2, max=100)],
                             render_kw={'placeholder': 'Search by username...'})
    submit = SubmitField('Search')


class ComparePlayersForm(FlaskForm):
    """Form for comparing players"""
    friend_id = SelectField('Compare With', 
                           coerce=int,
                           validators=[DataRequired()],
                           render_kw={'class': 'form-select'})
    game_id = SelectField('Game', 
                         coerce=int,
                         validators=[Optional()],
                         render_kw={'class': 'form-select'})
    time_period = SelectField('Time Period',
                             choices=[
                                 ('all', 'All Time'),
                                 ('30', 'Last 30 Days'),
                                 ('7', 'Last 7 Days')
                             ],
                             default='all',
                             render_kw={'class': 'form-select'})
    submit = SubmitField('Compare Stats')
    
    def __init__(self, user_id=None, *args, **kwargs):
        super(ComparePlayersForm, self).__init__(*args, **kwargs)
        
        if user_id:
            # Get user's friends for comparison
            from app.models.friendship import Friendship
            friendships = Friendship.query.filter(
                ((Friendship.user_id == user_id) | (Friendship.friend_id == user_id)) &
                (Friendship.status == 'accepted')
            ).all()
            
            friends = []
            for friendship in friendships:
                friend = friendship.friend if friendship.user_id == user_id else friendship.user
                friends.append((friend.id, friend.username))
            
            self.friend_id.choices = [('', 'Select a friend...')] + friends
            
            # Get available games
            games = Game.query.all()
            self.game_id.choices = [('', 'All Games')] + [(g.id, g.name) for g in games]


class RemoveFriendForm(FlaskForm):
    """Form for removing friends"""
    friend_id = HiddenField('Friend ID', validators=[DataRequired()])
    submit = SubmitField('Remove Friend')


class BlockUserForm(FlaskForm):
    """Form for blocking users"""
    user_id = HiddenField('User ID', validators=[DataRequired()])
    reason = TextAreaField('Reason (Optional)', 
                          validators=[Optional(), Length(max=500)],
                          render_kw={'placeholder': 'Why are you blocking this user?', 'rows': 3})
    submit = SubmitField('Block User')


class LeaderboardFilterForm(FlaskForm):
    """Form for filtering leaderboards"""
    game_id = SelectField('Game', 
                         coerce=int,
                         validators=[Optional()],
                         render_kw={'class': 'form-select'})
    metric = SelectField('Metric',
                        choices=[
                            ('kd_ratio', 'K/D Ratio'),
                            ('win_rate', 'Win Rate'),
                            ('total_matches', 'Total Matches'),
                            ('total_wins', 'Total Wins'),
                            ('avg_score', 'Average Score')
                        ],
                        default='kd_ratio',
                        render_kw={'class': 'form-select'})
    time_period = SelectField('Time Period',
                             choices=[
                                 ('all', 'All Time'),
                                 ('30', 'Last 30 Days'),
                                 ('7', 'Last 7 Days')
                             ],
                             default='all',
                             render_kw={'class': 'form-select'})
    friends_only = SelectField('Show',
                              choices=[
                                  ('all', 'All Players'),
                                  ('friends', 'Friends Only')
                              ],
                              default='all',
                              render_kw={'class': 'form-select'})
    submit = SubmitField('Apply Filters')
    
    def __init__(self, *args, **kwargs):
        super(LeaderboardFilterForm, self).__init__(*args, **kwargs)
        
        # Get available games
        games = Game.query.all()
        self.game_id.choices = [('', 'All Games')] + [(g.id, g.name) for g in games]
