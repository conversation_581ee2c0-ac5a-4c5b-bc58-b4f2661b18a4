"""
Forms for player profile management
"""
from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, TextAreaField, SubmitField, HiddenField
from wtforms.validators import DataRequired, Length, Optional, ValidationError
from app.models.game import Game
from app.models.player_profile import PlayerProfile
from app.services import get_service_for_game
from flask_login import current_user


class AddProfileForm(FlaskForm):
    """Form for adding a new game profile"""
    
    game_id = SelectField('Game', coerce=int, validators=[DataRequired()])
    player_name = StringField('Player Name/Username', validators=[
        DataRequired(),
        Length(min=3, max=50, message='Player name must be between 3 and 50 characters')
    ])
    player_tag = StringField('Player Tag (if applicable)', validators=[
        Optional(),
        Length(max=20, message='Player tag must be less than 20 characters')
    ])
    external_id = StringField('External ID (Steam ID, PUUID, etc.)', validators=[
        Optional(),
        Length(max=100, message='External ID must be less than 100 characters')
    ])
    region = SelectField('Region', choices=[
        ('na', 'North America'),
        ('eu', 'Europe'),
        ('ap', 'Asia Pacific'),
        ('kr', 'Korea'),
        ('br', 'Brazil'),
        ('latam', 'Latin America'),
        ('oce', 'Oceania')
    ], validators=[DataRequired()])
    notes = TextAreaField('Notes (optional)', validators=[
        Optional(),
        Length(max=500, message='Notes must be less than 500 characters')
    ])
    submit = SubmitField('Add Profile')
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate game choices
        self.game_id.choices = [(g.id, g.name) for g in Game.query.filter_by(is_active=True).all()]
    
    def validate_player_name(self, field):
        """Validate player name format based on selected game"""
        if self.game_id.data:
            game = Game.query.get(self.game_id.data)
            if game:
                # Get API service for validation
                api_service = get_service_for_game(game.api_name)
                if api_service and not api_service.validate_player_identifier(field.data):
                    raise ValidationError(f'Invalid player name format for {game.name}')
    
    def validate_external_id(self, field):
        """Validate external ID format if provided"""
        if field.data and self.game_id.data:
            game = Game.query.get(self.game_id.data)
            if game and game.api_name == 'cs2':  # Steam ID validation
                api_service = get_service_for_game('cs2')
                if api_service and not api_service.validate_player_identifier(field.data):
                    raise ValidationError('Invalid Steam ID format')
    
    def validate(self, extra_validators=None):
        """Custom validation"""
        if not super().validate(extra_validators):
            return False
        
        # Check if user already has a profile for this game
        if current_user.is_authenticated:
            existing_profile = PlayerProfile.query.filter_by(
                user_id=current_user.id,
                game_id=self.game_id.data
            ).first()
            
            if existing_profile:
                self.game_id.errors.append('You already have a profile for this game')
                return False
        
        return True


class EditProfileForm(FlaskForm):
    """Form for editing an existing game profile"""
    
    profile_id = HiddenField()
    player_name = StringField('Player Name/Username', validators=[
        DataRequired(),
        Length(min=3, max=50, message='Player name must be between 3 and 50 characters')
    ])
    player_tag = StringField('Player Tag (if applicable)', validators=[
        Optional(),
        Length(max=20, message='Player tag must be less than 20 characters')
    ])
    external_id = StringField('External ID (Steam ID, PUUID, etc.)', validators=[
        Optional(),
        Length(max=100, message='External ID must be less than 100 characters')
    ])
    region = SelectField('Region', choices=[
        ('na', 'North America'),
        ('eu', 'Europe'),
        ('ap', 'Asia Pacific'),
        ('kr', 'Korea'),
        ('br', 'Brazil'),
        ('latam', 'Latin America'),
        ('oce', 'Oceania')
    ], validators=[DataRequired()])
    notes = TextAreaField('Notes (optional)', validators=[
        Optional(),
        Length(max=500, message='Notes must be less than 500 characters')
    ])
    is_active = SelectField('Status', choices=[
        (True, 'Active'),
        (False, 'Inactive')
    ], coerce=lambda x: x == 'True', validators=[DataRequired()])
    submit = SubmitField('Update Profile')
    
    def __init__(self, profile=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if profile:
            self.profile = profile
            self.populate_from_profile(profile)
    
    def populate_from_profile(self, profile):
        """Populate form fields from existing profile"""
        self.profile_id.data = profile.id
        self.player_name.data = profile.player_name
        self.player_tag.data = profile.player_tag
        self.external_id.data = profile.external_id
        self.region.data = profile.region
        self.notes.data = profile.notes
        self.is_active.data = profile.is_active
    
    def validate_player_name(self, field):
        """Validate player name format"""
        if hasattr(self, 'profile') and self.profile:
            api_service = get_service_for_game(self.profile.game.api_name)
            if api_service and not api_service.validate_player_identifier(field.data):
                raise ValidationError(f'Invalid player name format for {self.profile.game.name}')


class UpdateStatsForm(FlaskForm):
    """Form for manually triggering stats update"""
    
    profile_id = HiddenField(validators=[DataRequired()])
    submit = SubmitField('Update Stats')


class DeleteProfileForm(FlaskForm):
    """Form for deleting a profile"""
    
    profile_id = HiddenField(validators=[DataRequired()])
    confirm = StringField('Type "DELETE" to confirm', validators=[
        DataRequired(),
        Length(min=6, max=6, message='Please type DELETE exactly')
    ])
    submit = SubmitField('Delete Profile')
    
    def validate_confirm(self, field):
        """Validate confirmation text"""
        if field.data != 'DELETE':
            raise ValidationError('Please type DELETE exactly to confirm deletion')


class SearchPlayerForm(FlaskForm):
    """Form for searching players"""
    
    game_id = SelectField('Game', coerce=int, validators=[DataRequired()])
    search_query = StringField('Player Name or ID', validators=[
        DataRequired(),
        Length(min=3, max=50, message='Search query must be between 3 and 50 characters')
    ])
    region = SelectField('Region', choices=[
        ('', 'Any Region'),
        ('na', 'North America'),
        ('eu', 'Europe'),
        ('ap', 'Asia Pacific'),
        ('kr', 'Korea'),
        ('br', 'Brazil'),
        ('latam', 'Latin America'),
        ('oce', 'Oceania')
    ], validators=[Optional()])
    submit = SubmitField('Search')
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate game choices
        self.game_id.choices = [(g.id, g.name) for g in Game.query.filter_by(is_active=True).all()]
