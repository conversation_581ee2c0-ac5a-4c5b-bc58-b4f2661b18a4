"""
Redis caching utilities for the application
"""
import redis
import json
import pickle
from functools import wraps
from datetime import datetime, timedelta
from flask import current_app
import logging

logger = logging.getLogger(__name__)


class CacheManager:
    """Redis cache manager for the application"""
    
    def __init__(self, app=None):
        self.redis_client = None
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize Redis connection with Flask app"""
        try:
            redis_url = app.config.get('REDIS_URL', 'redis://localhost:6379/0')
            self.redis_client = redis.from_url(redis_url, decode_responses=True)
            
            # Test connection
            self.redis_client.ping()
            logger.info("Redis cache initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Redis cache: {e}")
            self.redis_client = None
    
    def get(self, key):
        """Get value from cache"""
        if not self.redis_client:
            return None
        
        try:
            value = self.redis_client.get(key)
            if value:
                return json.loads(value)
            return None
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            return None
    
    def set(self, key, value, timeout=300):
        """Set value in cache with timeout (default 5 minutes)"""
        if not self.redis_client:
            return False
        
        try:
            serialized_value = json.dumps(value, default=str)
            return self.redis_client.setex(key, timeout, serialized_value)
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            return False
    
    def delete(self, key):
        """Delete key from cache"""
        if not self.redis_client:
            return False
        
        try:
            return self.redis_client.delete(key)
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False
    
    def clear_pattern(self, pattern):
        """Clear all keys matching pattern"""
        if not self.redis_client:
            return False
        
        try:
            keys = self.redis_client.keys(pattern)
            if keys:
                return self.redis_client.delete(*keys)
            return True
        except Exception as e:
            logger.error(f"Cache clear pattern error for {pattern}: {e}")
            return False
    
    def get_or_set(self, key, callback, timeout=300):
        """Get from cache or set using callback if not found"""
        value = self.get(key)
        if value is not None:
            return value
        
        # Generate value using callback
        try:
            value = callback()
            self.set(key, value, timeout)
            return value
        except Exception as e:
            logger.error(f"Cache get_or_set error for key {key}: {e}")
            return None


# Global cache instance
cache = CacheManager()


def cached(timeout=300, key_prefix=''):
    """
    Decorator for caching function results
    
    Args:
        timeout (int): Cache timeout in seconds
        key_prefix (str): Prefix for cache key
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            key_parts = [key_prefix, func.__name__]
            
            # Add args to key
            for arg in args:
                if hasattr(arg, 'id'):
                    key_parts.append(str(arg.id))
                else:
                    key_parts.append(str(arg))
            
            # Add kwargs to key
            for k, v in sorted(kwargs.items()):
                key_parts.append(f"{k}:{v}")
            
            cache_key = ':'.join(filter(None, key_parts))
            
            # Try to get from cache
            result = cache.get(cache_key)
            if result is not None:
                logger.debug(f"Cache hit for key: {cache_key}")
                return result
            
            # Execute function and cache result
            logger.debug(f"Cache miss for key: {cache_key}")
            result = func(*args, **kwargs)
            cache.set(cache_key, result, timeout)
            
            return result
        
        return wrapper
    return decorator


def cache_api_response(api_name, endpoint, params=None, timeout=300):
    """
    Cache API response with structured key
    
    Args:
        api_name (str): Name of the API (e.g., 'valorant', 'steam')
        endpoint (str): API endpoint
        params (dict): API parameters
        timeout (int): Cache timeout in seconds
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            key_parts = ['api', api_name, endpoint]
            
            if params:
                for k, v in sorted(params.items()):
                    key_parts.append(f"{k}:{v}")
            
            # Add function args to key
            for arg in args:
                if isinstance(arg, (str, int, float)):
                    key_parts.append(str(arg))
            
            cache_key = ':'.join(key_parts)
            
            # Try to get from cache
            result = cache.get(cache_key)
            if result is not None:
                logger.debug(f"API cache hit for: {cache_key}")
                return result
            
            # Execute function and cache result
            logger.debug(f"API cache miss for: {cache_key}")
            result = func(*args, **kwargs)
            
            # Only cache successful responses
            if result and not isinstance(result, Exception):
                cache.set(cache_key, result, timeout)
            
            return result
        
        return wrapper
    return decorator


def invalidate_user_cache(user_id):
    """Invalidate all cache entries for a specific user"""
    patterns = [
        f"user:{user_id}:*",
        f"stats:{user_id}:*",
        f"profile:{user_id}:*",
        f"matches:{user_id}:*"
    ]
    
    for pattern in patterns:
        cache.clear_pattern(pattern)
    
    logger.info(f"Invalidated cache for user {user_id}")


def invalidate_profile_cache(profile_id):
    """Invalidate cache entries for a specific profile"""
    patterns = [
        f"profile:{profile_id}:*",
        f"stats:profile:{profile_id}:*",
        f"matches:profile:{profile_id}:*"
    ]
    
    for pattern in patterns:
        cache.clear_pattern(pattern)
    
    logger.info(f"Invalidated cache for profile {profile_id}")


def get_cache_stats():
    """Get cache statistics"""
    if not cache.redis_client:
        return None
    
    try:
        info = cache.redis_client.info()
        return {
            'connected_clients': info.get('connected_clients', 0),
            'used_memory': info.get('used_memory_human', '0B'),
            'keyspace_hits': info.get('keyspace_hits', 0),
            'keyspace_misses': info.get('keyspace_misses', 0),
            'total_commands_processed': info.get('total_commands_processed', 0)
        }
    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        return None
