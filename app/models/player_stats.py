"""
Player statistics model for aggregated stats
"""
from datetime import datetime, timedelta
from app import db


class PlayerStats(db.Model):
    """Model for aggregated player statistics"""
    __tablename__ = 'player_stats'
    
    id = db.Column(db.Integer, primary_key=True)
    player_profile_id = db.Column(db.<PERSON>, db.<PERSON>ey('player_profiles.id'), nullable=False)
    
    # Time period for these stats
    period_type = db.Column(db.String(20), nullable=False)  # 'daily', 'weekly', 'monthly', 'season'
    period_start = db.Column(db.DateTime, nullable=False)
    period_end = db.Column(db.DateTime, nullable=False)
    
    # Match statistics
    matches_played = db.Column(db.Integer, default=0)
    matches_won = db.Column(db.Integer, default=0)
    matches_lost = db.Column(db.Integer, default=0)
    matches_drawn = db.Column(db.Integer, default=0)
    
    # Performance statistics
    total_kills = db.Column(db.Integer, default=0)
    total_deaths = db.Column(db.Integer, default=0)
    total_assists = db.Column(db.Integer, default=0)
    total_score = db.Column(db.Integer, default=0)
    
    # Calculated metrics
    avg_kills = db.Column(db.Float)
    avg_deaths = db.Column(db.Float)
    avg_assists = db.Column(db.Float)
    avg_score = db.Column(db.Float)
    kd_ratio = db.Column(db.Float)
    kda_ratio = db.Column(db.Float)
    win_rate = db.Column(db.Float)
    
    # Rank progression
    highest_rank = db.Column(db.String(50))
    lowest_rank = db.Column(db.String(50))
    rank_changes = db.Column(db.Integer, default=0)  # Number of rank changes
    rating_gained = db.Column(db.Integer, default=0)
    rating_lost = db.Column(db.Integer, default=0)
    net_rating_change = db.Column(db.Integer, default=0)
    
    # Game-specific aggregated stats
    game_specific_stats = db.Column(db.JSON)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Unique constraint
    __table_args__ = (
        db.UniqueConstraint('player_profile_id', 'period_type', 'period_start', 
                          name='unique_player_stats_period'),
    )
    
    def calculate_averages(self):
        """Calculate average statistics"""
        if self.matches_played == 0:
            return
        
        self.avg_kills = round(self.total_kills / self.matches_played, 2)
        self.avg_deaths = round(self.total_deaths / self.matches_played, 2)
        self.avg_assists = round(self.total_assists / self.matches_played, 2)
        self.avg_score = round(self.total_score / self.matches_played, 2)
    
    def calculate_ratios(self):
        """Calculate K/D and KDA ratios"""
        if self.total_deaths == 0:
            self.kd_ratio = float(self.total_kills) if self.total_kills > 0 else 0.0
            self.kda_ratio = float(self.total_kills + self.total_assists) if (self.total_kills + self.total_assists) > 0 else 0.0
        else:
            self.kd_ratio = round(self.total_kills / self.total_deaths, 2)
            self.kda_ratio = round((self.total_kills + self.total_assists) / self.total_deaths, 2)
    
    def calculate_win_rate(self):
        """Calculate win rate percentage"""
        if self.matches_played == 0:
            self.win_rate = 0.0
        else:
            self.win_rate = round((self.matches_won / self.matches_played) * 100, 2)
    
    def update_all_calculations(self):
        """Update all calculated fields"""
        self.calculate_averages()
        self.calculate_ratios()
        self.calculate_win_rate()
        self.net_rating_change = self.rating_gained - self.rating_lost
        self.updated_at = datetime.utcnow()
    
    def to_dict(self):
        """Convert player stats to dictionary"""
        return {
            'id': self.id,
            'player_profile_id': self.player_profile_id,
            'period_type': self.period_type,
            'period_start': self.period_start.isoformat() if self.period_start else None,
            'period_end': self.period_end.isoformat() if self.period_end else None,
            'matches_played': self.matches_played,
            'matches_won': self.matches_won,
            'matches_lost': self.matches_lost,
            'matches_drawn': self.matches_drawn,
            'total_kills': self.total_kills,
            'total_deaths': self.total_deaths,
            'total_assists': self.total_assists,
            'total_score': self.total_score,
            'avg_kills': self.avg_kills,
            'avg_deaths': self.avg_deaths,
            'avg_assists': self.avg_assists,
            'avg_score': self.avg_score,
            'kd_ratio': self.kd_ratio,
            'kda_ratio': self.kda_ratio,
            'win_rate': self.win_rate,
            'highest_rank': self.highest_rank,
            'lowest_rank': self.lowest_rank,
            'net_rating_change': self.net_rating_change,
            'game_specific_stats': self.game_specific_stats
        }
    
    @staticmethod
    def create_period_stats(player_profile_id, period_type, start_date, end_date):
        """Create stats for a specific period"""
        from app.models.match import Match
        
        # Get matches in the period
        matches = Match.query.filter(
            Match.player_profile_id == player_profile_id,
            Match.match_date >= start_date,
            Match.match_date <= end_date
        ).all()
        
        if not matches:
            return None
        
        # Calculate aggregated stats
        stats = PlayerStats(
            player_profile_id=player_profile_id,
            period_type=period_type,
            period_start=start_date,
            period_end=end_date
        )
        
        stats.matches_played = len(matches)
        stats.matches_won = len([m for m in matches if m.result == 'win'])
        stats.matches_lost = len([m for m in matches if m.result == 'loss'])
        stats.matches_drawn = len([m for m in matches if m.result == 'draw'])
        
        stats.total_kills = sum(m.kills for m in matches)
        stats.total_deaths = sum(m.deaths for m in matches)
        stats.total_assists = sum(m.assists for m in matches)
        stats.total_score = sum(m.score for m in matches)
        
        # Calculate rating changes
        rating_changes = [m.rating_change for m in matches if m.rating_change is not None]
        stats.rating_gained = sum(r for r in rating_changes if r > 0)
        stats.rating_lost = abs(sum(r for r in rating_changes if r < 0))
        
        stats.update_all_calculations()
        
        return stats
    
    def __repr__(self):
        return f'<PlayerStats {self.player_profile_id}: {self.period_type} ({self.period_start} - {self.period_end})>'
