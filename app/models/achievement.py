"""
Achievement system models
"""
from datetime import datetime
from app import db


class Achievement(db.Model):
    """Model for available achievements"""
    __tablename__ = 'achievements'
    
    id = db.Column(db.Integer, primary_key=True)
    game_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('games.id'), nullable=False)
    
    # Achievement details
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=False)
    category = db.Column(db.String(50))  # 'performance', 'milestone', 'special'
    
    # Achievement criteria (stored as JSON)
    criteria = db.Column(db.JSON, nullable=False)
    
    # Display settings
    icon_url = db.Column(db.String(255))
    badge_color = db.Column(db.String(7))  # Hex color
    rarity = db.Column(db.String(20), default='common')  # 'common', 'rare', 'epic', 'legendary'
    
    # Points and rewards
    points = db.Column(db.Integer, default=10)
    is_hidden = db.Column(db.<PERSON>, default=False)  # Hidden until unlocked
    
    # Status
    is_active = db.Column(db.<PERSON>, default=True)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user_achievements = db.relationship('UserAchievement', backref='achievement', lazy='dynamic')
    
    def get_unlock_count(self):
        """Get number of users who unlocked this achievement"""
        return self.user_achievements.count()
    
    def get_unlock_percentage(self):
        """Get percentage of users who unlocked this achievement"""
        from app.models.user import User
        total_users = User.query.count()
        if total_users == 0:
            return 0.0
        return round((self.get_unlock_count() / total_users) * 100, 2)
    
    def to_dict(self):
        """Convert achievement to dictionary"""
        return {
            'id': self.id,
            'game_id': self.game_id,
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'criteria': self.criteria,
            'icon_url': self.icon_url,
            'badge_color': self.badge_color,
            'rarity': self.rarity,
            'points': self.points,
            'is_hidden': self.is_hidden,
            'unlock_count': self.get_unlock_count(),
            'unlock_percentage': self.get_unlock_percentage()
        }
    
    def __repr__(self):
        return f'<Achievement {self.name} ({self.game.name})>'


class UserAchievement(db.Model):
    """Model for user's unlocked achievements"""
    __tablename__ = 'user_achievements'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    achievement_id = db.Column(db.Integer, db.ForeignKey('achievements.id'), nullable=False)
    
    # Unlock details
    unlocked_at = db.Column(db.DateTime, default=datetime.utcnow)
    progress_data = db.Column(db.JSON)  # Data that led to unlock
    
    # Notification status
    is_notified = db.Column(db.Boolean, default=False)
    
    # Unique constraint
    __table_args__ = (db.UniqueConstraint('user_id', 'achievement_id', name='unique_user_achievement'),)
    
    def to_dict(self):
        """Convert user achievement to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'achievement_id': self.achievement_id,
            'achievement': self.achievement.to_dict() if self.achievement else None,
            'unlocked_at': self.unlocked_at.isoformat() if self.unlocked_at else None,
            'progress_data': self.progress_data,
            'is_notified': self.is_notified
        }
    
    def __repr__(self):
        return f'<UserAchievement {self.user.username}: {self.achievement.name}>'


class AchievementProgress(db.Model):
    """Model for tracking achievement progress"""
    __tablename__ = 'achievement_progress'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    achievement_id = db.Column(db.Integer, db.ForeignKey('achievements.id'), nullable=False)
    
    # Progress tracking
    current_progress = db.Column(db.JSON)  # Current progress data
    progress_percentage = db.Column(db.Float, default=0.0)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref='achievement_progress')
    achievement = db.relationship('Achievement', backref='progress_tracking')
    
    # Unique constraint
    __table_args__ = (db.UniqueConstraint('user_id', 'achievement_id', name='unique_user_achievement_progress'),)
    
    def update_progress(self, progress_data):
        """Update progress and calculate percentage"""
        self.current_progress = progress_data
        self.progress_percentage = self.calculate_progress_percentage()
        self.updated_at = datetime.utcnow()
        
        # Check if achievement should be unlocked
        if self.progress_percentage >= 100.0:
            return self.unlock_achievement()
        
        return False
    
    def calculate_progress_percentage(self):
        """Calculate progress percentage based on criteria"""
        if not self.current_progress or not self.achievement.criteria:
            return 0.0
        
        # This would be implemented based on specific achievement criteria
        # For now, return a simple calculation
        return min(100.0, self.progress_percentage)
    
    def unlock_achievement(self):
        """Unlock the achievement for the user"""
        # Check if already unlocked
        existing = UserAchievement.query.filter_by(
            user_id=self.user_id,
            achievement_id=self.achievement_id
        ).first()
        
        if existing:
            return False
        
        # Create user achievement
        user_achievement = UserAchievement(
            user_id=self.user_id,
            achievement_id=self.achievement_id,
            progress_data=self.current_progress
        )
        
        db.session.add(user_achievement)
        return True
    
    def to_dict(self):
        """Convert achievement progress to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'achievement_id': self.achievement_id,
            'achievement': self.achievement.to_dict() if self.achievement else None,
            'current_progress': self.current_progress,
            'progress_percentage': self.progress_percentage,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f'<AchievementProgress {self.user.username}: {self.achievement.name} ({self.progress_percentage}%)>'
