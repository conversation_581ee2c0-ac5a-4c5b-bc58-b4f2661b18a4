"""
Friendship system model
"""
from datetime import datetime
from app import db


class Friendship(db.Model):
    """Model for user friendships"""
    __tablename__ = 'friendships'
    
    id = db.<PERSON>umn(db.<PERSON>teger, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('users.id'), nullable=False)
    friend_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    
    # Friendship status
    status = db.Column(db.String(20), default='pending')  # 'pending', 'accepted', 'blocked'
    message = db.Column(db.Text)  # Optional message with friend request
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', foreign_keys=[user_id], backref='sent_friend_requests')
    friend = db.relationship('User', foreign_keys=[friend_id], backref='received_friend_requests')

    # Unique constraint to prevent duplicate friendships
    __table_args__ = (
        db.UniqueConstraint('user_id', 'friend_id', name='unique_friendship'),
        db.CheckConstraint('user_id != friend_id', name='no_self_friendship')
    )
    
    def accept(self):
        """Accept the friendship request"""
        self.status = 'accepted'
        self.updated_at = datetime.utcnow()
    
    def block(self):
        """Block the friendship"""
        self.status = 'blocked'
        self.updated_at = datetime.utcnow()
    
    def to_dict(self):
        """Convert friendship to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'friend_id': self.friend_id,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @staticmethod
    def send_friend_request(user_id, friend_id):
        """Send a friend request"""
        # Check if friendship already exists
        existing = Friendship.query.filter(
            db.or_(
                db.and_(Friendship.user_id == user_id, Friendship.friend_id == friend_id),
                db.and_(Friendship.user_id == friend_id, Friendship.friend_id == user_id)
            )
        ).first()
        
        if existing:
            return existing, False  # Already exists
        
        # Create new friendship request
        friendship = Friendship(user_id=user_id, friend_id=friend_id)
        db.session.add(friendship)
        return friendship, True
    
    @staticmethod
    def get_pending_requests(user_id):
        """Get pending friend requests for a user"""
        return Friendship.query.filter_by(friend_id=user_id, status='pending').all()
    
    @staticmethod
    def get_sent_requests(user_id):
        """Get sent friend requests by a user"""
        return Friendship.query.filter_by(user_id=user_id, status='pending').all()
    
    @staticmethod
    def get_friends(user_id):
        """Get all accepted friends for a user"""
        sent_friends = db.session.query(Friendship).filter(
            Friendship.user_id == user_id,
            Friendship.status == 'accepted'
        ).all()
        
        received_friends = db.session.query(Friendship).filter(
            Friendship.friend_id == user_id,
            Friendship.status == 'accepted'
        ).all()
        
        return sent_friends + received_friends
    
    @staticmethod
    def are_friends(user_id, friend_id):
        """Check if two users are friends"""
        friendship = Friendship.query.filter(
            ((Friendship.user_id == user_id) & (Friendship.friend_id == friend_id)) |
            ((Friendship.user_id == friend_id) & (Friendship.friend_id == user_id))
        ).filter_by(status='accepted').first()
        return friendship is not None

    @staticmethod
    def get_friendship(user_id, friend_id):
        """Get friendship between two users (bidirectional)"""
        return Friendship.query.filter(
            ((Friendship.user_id == user_id) & (Friendship.friend_id == friend_id)) |
            ((Friendship.user_id == friend_id) & (Friendship.friend_id == user_id))
        ).first()

    @staticmethod
    def remove_friend(user_id, friend_id):
        """Remove a friend"""
        friendship = Friendship.get_friendship(user_id, friend_id)
        if friendship and friendship.status == 'accepted':
            try:
                db.session.delete(friendship)
                db.session.commit()
                return True, "Friend removed successfully"
            except Exception as e:
                db.session.rollback()
                return False, f"Error removing friend: {str(e)}"
        return False, "Friendship not found"

    @staticmethod
    def block_user(user_id, blocked_user_id, reason=None):
        """Block a user"""
        # Remove existing friendship if any
        existing = Friendship.get_friendship(user_id, blocked_user_id)
        if existing:
            db.session.delete(existing)

        # Create block relationship
        block = Friendship(
            user_id=user_id,
            friend_id=blocked_user_id,
            status='blocked',
            message=reason
        )

        try:
            db.session.add(block)
            db.session.commit()
            return True, "User blocked successfully"
        except Exception as e:
            db.session.rollback()
            return False, f"Error blocking user: {str(e)}"

    def __repr__(self):
        return f'<Friendship {self.user_id} -> {self.friend_id} ({self.status})>'
