"""
Game model for supported games
"""
from datetime import datetime
from app import db


class Game(db.Model):
    """Model for supported games"""
    __tablename__ = 'games'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    slug = db.Column(db.String(50), unique=True, nullable=False, index=True)
    description = db.Column(db.Text)
    
    # Game metadata
    developer = db.Column(db.String(100))
    genre = db.Column(db.String(50))
    release_date = db.Column(db.Date)
    
    # API configuration
    api_provider = db.Column(db.String(50))  # 'riot', 'steam', 'custom'
    api_base_url = db.Column(db.String(255))
    api_version = db.Column(db.String(20))
    
    # Display settings
    icon_url = db.Column(db.String(255))
    banner_url = db.Column(db.String(255))
    primary_color = db.Column(db.String(7))  # Hex color code
    
    # Status
    is_active = db.Column(db.Boolean, default=True)
    is_featured = db.Column(db.Boolean, default=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    player_profiles = db.relationship('PlayerProfile', backref='game', lazy='dynamic')
    matches = db.relationship('Match', backref='game', lazy='dynamic')
    achievements = db.relationship('Achievement', backref='game', lazy='dynamic')
    
    def to_dict(self):
        """Convert game to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'slug': self.slug,
            'description': self.description,
            'developer': self.developer,
            'genre': self.genre,
            'release_date': self.release_date.isoformat() if self.release_date else None,
            'api_provider': self.api_provider,
            'icon_url': self.icon_url,
            'banner_url': self.banner_url,
            'primary_color': self.primary_color,
            'is_active': self.is_active,
            'is_featured': self.is_featured
        }
    
    @staticmethod
    def get_active_games():
        """Get all active games"""
        return Game.query.filter_by(is_active=True).all()
    
    @staticmethod
    def get_featured_games():
        """Get featured games"""
        return Game.query.filter_by(is_active=True, is_featured=True).all()
    
    def __repr__(self):
        return f'<Game {self.name}>'


class GameMode(db.Model):
    """Model for game modes within games"""
    __tablename__ = 'game_modes'
    
    id = db.Column(db.Integer, primary_key=True)
    game_id = db.Column(db.Integer, db.ForeignKey('games.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    slug = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text)
    
    # Mode settings
    is_ranked = db.Column(db.Boolean, default=False)
    is_active = db.Column(db.Boolean, default=True)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    game = db.relationship('Game', backref='game_modes')
    matches = db.relationship('Match', backref='game_mode', lazy='dynamic')
    
    # Unique constraint
    __table_args__ = (db.UniqueConstraint('game_id', 'slug', name='unique_game_mode_slug'),)
    
    def to_dict(self):
        """Convert game mode to dictionary"""
        return {
            'id': self.id,
            'game_id': self.game_id,
            'name': self.name,
            'slug': self.slug,
            'description': self.description,
            'is_ranked': self.is_ranked,
            'is_active': self.is_active
        }
    
    def __repr__(self):
        return f'<GameMode {self.game.name}: {self.name}>'
