"""
Models package for Game Stats Tracker
"""

# Import all models to ensure they are registered with SQLAlchemy
from app.models.user import User
from app.models.game import Game, GameMode
from app.models.player_profile import PlayerProfile
from app.models.match import Match
from app.models.player_stats import PlayerStats
from app.models.achievement import Achievement, UserAchievement, AchievementProgress
from app.models.friendship import Friendship

# Export all models
__all__ = [
    'User',
    'Game',
    'GameMode',
    'PlayerProfile',
    'Match',
    'PlayerStats',
    'Achievement',
    'UserAchievement',
    'AchievementProgress',
    'Friendship'
]