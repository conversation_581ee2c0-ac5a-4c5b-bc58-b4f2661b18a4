"""
Player profile model for game-specific user profiles
"""
from datetime import datetime
from app import db


class PlayerProfile(db.Model):
    """Model for user's game-specific profiles"""
    __tablename__ = 'player_profiles'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON>ger, db.<PERSON>ey('users.id'), nullable=False)
    game_id = db.Column(db.Integer, db.ForeignKey('games.id'), nullable=False)
    
    # Player identifiers
    player_name = db.Column(db.String(100), nullable=False)  # In-game username
    player_tag = db.Column(db.String(20))  # Tag/discriminator (e.g., #1234)
    player_id = db.Column(db.String(100))  # Game-specific player ID
    
    # External account linking
    riot_puuid = db.Column(db.String(100))  # Riot PUUID
    steam_id = db.Column(db.String(100))    # Steam ID
    
    # Profile settings
    is_primary = db.Column(db.<PERSON>olean, default=False)  # Primary profile for this game
    is_public = db.Column(db.<PERSON>, default=True)    # Public visibility
    is_verified = db.Column(db.Boolean, default=False) # Verified account
    
    # Current stats (cached for quick access)
    current_rank = db.Column(db.String(50))
    current_rank_tier = db.Column(db.Integer)
    current_rating = db.Column(db.Integer)
    
    # Lifetime stats
    total_matches = db.Column(db.Integer, default=0)
    total_wins = db.Column(db.Integer, default=0)
    total_losses = db.Column(db.Integer, default=0)
    total_kills = db.Column(db.Integer, default=0)
    total_deaths = db.Column(db.Integer, default=0)
    total_assists = db.Column(db.Integer, default=0)
    
    # Performance metrics
    kd_ratio = db.Column(db.Float)
    win_rate = db.Column(db.Float)
    average_score = db.Column(db.Float)
    
    # Last update info
    last_match_date = db.Column(db.DateTime)
    last_updated = db.Column(db.DateTime, default=datetime.utcnow)
    update_frequency = db.Column(db.Integer, default=3600)  # seconds
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    matches = db.relationship('Match', backref='player_profile', lazy='dynamic')
    stats = db.relationship('PlayerStats', backref='player_profile', lazy='dynamic', cascade='all, delete-orphan')
    
    # Unique constraint
    __table_args__ = (db.UniqueConstraint('user_id', 'game_id', 'player_name', name='unique_user_game_player'),)
    
    def calculate_kd_ratio(self):
        """Calculate K/D ratio"""
        if self.total_deaths == 0:
            return float(self.total_kills) if self.total_kills > 0 else 0.0
        return round(self.total_kills / self.total_deaths, 2)
    
    def calculate_win_rate(self):
        """Calculate win rate percentage"""
        if self.total_matches == 0:
            return 0.0
        return round((self.total_wins / self.total_matches) * 100, 2)
    
    def update_stats(self):
        """Update calculated stats"""
        self.kd_ratio = self.calculate_kd_ratio()
        self.win_rate = self.calculate_win_rate()
        self.last_updated = datetime.utcnow()
    
    def get_recent_matches(self, limit=10):
        """Get recent matches for this profile"""
        return self.matches.order_by(Match.match_date.desc()).limit(limit).all()
    
    def get_rank_display(self):
        """Get formatted rank display"""
        if self.current_rank:
            if self.current_rank_tier:
                return f"{self.current_rank} {self.current_rank_tier}"
            return self.current_rank
        return "Unranked"
    
    def get_full_player_name(self):
        """Get full player name with tag"""
        if self.player_tag:
            return f"{self.player_name}#{self.player_tag}"
        return self.player_name
    
    def needs_update(self):
        """Check if profile needs stats update"""
        if not self.last_updated:
            return True
        
        time_since_update = datetime.utcnow() - self.last_updated
        return time_since_update.total_seconds() > self.update_frequency
    
    def to_dict(self):
        """Convert player profile to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'game_id': self.game_id,
            'player_name': self.player_name,
            'player_tag': self.player_tag,
            'full_player_name': self.get_full_player_name(),
            'current_rank': self.current_rank,
            'rank_display': self.get_rank_display(),
            'current_rating': self.current_rating,
            'total_matches': self.total_matches,
            'total_wins': self.total_wins,
            'total_losses': self.total_losses,
            'kd_ratio': self.kd_ratio,
            'win_rate': self.win_rate,
            'is_primary': self.is_primary,
            'is_public': self.is_public,
            'is_verified': self.is_verified,
            'last_match_date': self.last_match_date.isoformat() if self.last_match_date else None,
            'last_updated': self.last_updated.isoformat() if self.last_updated else None
        }
    
    def __repr__(self):
        return f'<PlayerProfile {self.user.username}: {self.get_full_player_name()} ({self.game.name})>'


# Import here to avoid circular imports
# from app.models.match import Match
# from app.models.player_stats import PlayerStats
