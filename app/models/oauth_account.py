"""
OAuth account model for storing linked external accounts
"""
from datetime import datetime
from app import db


class OAuthAccount(db.Model):
    """Model for OAuth linked accounts"""
    __tablename__ = 'oauth_accounts'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    provider = db.Column(db.String(50), nullable=False)  # 'steam', 'riot', etc.
    provider_user_id = db.Column(db.String(255), nullable=False)  # External user ID
    access_token = db.Column(db.Text)  # OAuth access token
    refresh_token = db.Column(db.Text)  # OAuth refresh token
    token_expires_at = db.Column(db.DateTime)  # Token expiration
    user_info = db.Column(db.JSON)  # Additional user info from provider
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref='oauth_accounts')
    
    # Constraints
    __table_args__ = (
        db.UniqueConstraint('provider', 'provider_user_id', name='unique_provider_account'),
        db.Index('idx_user_provider', 'user_id', 'provider'),
    )
    
    def __repr__(self):
        return f'<OAuthAccount {self.provider}:{self.provider_user_id} -> User {self.user_id}>'
    
    def to_dict(self):
        """Convert OAuth account to dictionary"""
        return {
            'id': self.id,
            'provider': self.provider,
            'provider_user_id': self.provider_user_id,
            'user_info': self.user_info,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @property
    def is_token_expired(self):
        """Check if access token is expired"""
        if not self.token_expires_at:
            return False
        return datetime.utcnow() >= self.token_expires_at
    
    @property
    def display_name(self):
        """Get display name from user info"""
        if not self.user_info:
            return f"{self.provider}:{self.provider_user_id}"
        
        if self.provider == 'steam':
            return self.user_info.get('username', 'Steam User')
        elif self.provider == 'riot':
            game_name = self.user_info.get('game_name')
            tag_line = self.user_info.get('tag_line')
            if game_name and tag_line:
                return f"{game_name}#{tag_line}"
            return self.user_info.get('preferred_username', 'Riot User')
        
        return f"{self.provider.title()} User"
    
    @property
    def profile_url(self):
        """Get profile URL if available"""
        if not self.user_info:
            return None
        
        if self.provider == 'steam':
            return self.user_info.get('profile_url')
        elif self.provider == 'riot':
            # Riot doesn't provide public profile URLs
            return None
        
        return None
    
    @property
    def avatar_url(self):
        """Get avatar URL if available"""
        if not self.user_info:
            return None
        
        if self.provider == 'steam':
            return self.user_info.get('avatar')
        elif self.provider == 'riot':
            # Riot doesn't provide avatar URLs in basic user info
            return None
        
        return None
    
    @staticmethod
    def get_by_provider_user_id(provider, provider_user_id):
        """Get OAuth account by provider and external user ID"""
        return OAuthAccount.query.filter_by(
            provider=provider,
            provider_user_id=str(provider_user_id)
        ).first()
    
    @staticmethod
    def get_user_accounts(user_id, provider=None):
        """Get OAuth accounts for a user"""
        query = OAuthAccount.query.filter_by(user_id=user_id)
        if provider:
            query = query.filter_by(provider=provider)
        return query.all()
    
    @staticmethod
    def is_provider_linked(user_id, provider):
        """Check if user has linked account for provider"""
        return OAuthAccount.query.filter_by(
            user_id=user_id,
            provider=provider
        ).first() is not None
    
    def update_user_info(self, user_info):
        """Update user info from provider"""
        self.user_info = user_info
        self.updated_at = datetime.utcnow()
    
    def update_tokens(self, access_token, refresh_token=None, expires_at=None):
        """Update OAuth tokens"""
        self.access_token = access_token
        if refresh_token:
            self.refresh_token = refresh_token
        if expires_at:
            self.token_expires_at = expires_at
        self.updated_at = datetime.utcnow()
    
    def get_game_profiles(self):
        """Get game profiles that might be associated with this OAuth account"""
        from app.models.player_profile import PlayerProfile
        
        profiles = []
        
        if self.provider == 'steam':
            # Look for Steam-based game profiles
            steam_games = ['cs2']  # Add other Steam games as needed
            for game_name in steam_games:
                profile = PlayerProfile.query.filter_by(
                    user_id=self.user_id,
                    game_id=game_name
                ).first()
                if profile:
                    profiles.append(profile)
        
        elif self.provider == 'riot':
            # Look for Riot-based game profiles
            riot_games = ['valorant']  # Add other Riot games as needed
            for game_name in riot_games:
                profile = PlayerProfile.query.filter_by(
                    user_id=self.user_id,
                    game_id=game_name
                ).first()
                if profile:
                    profiles.append(profile)
        
        return profiles
    
    def can_auto_sync(self):
        """Check if this account can be used for automatic data syncing"""
        # Check if token is valid and not expired
        if self.is_token_expired:
            return False
        
        # Check if we have necessary permissions/scopes
        if self.provider == 'riot':
            # For Riot, we need valid access token
            return bool(self.access_token)
        elif self.provider == 'steam':
            # For Steam, we need API key configured and valid Steam ID
            from flask import current_app
            return bool(current_app.config.get('STEAM_API_KEY') and self.provider_user_id)
        
        return False
