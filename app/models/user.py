"""
User model for authentication and profile management
"""
from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app import db


class User(UserMixin, db.Model):
    """User model for authentication"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    
    # Profile information
    first_name = db.Column(db.String(50))
    last_name = db.Column(db.String(50))
    avatar_url = db.Column(db.String(255))
    bio = db.Column(db.Text)
    
    # Account status
    is_active = db.Column(db.<PERSON>, default=True)
    is_verified = db.Column(db.Bo<PERSON>, default=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    # Relationships
    player_profiles = db.relationship('PlayerProfile', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    achievements = db.relationship('UserAchievement', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    friendships_sent = db.relationship('Friendship', foreign_keys='Friendship.user_id', backref='sender', lazy='dynamic')
    friendships_received = db.relationship('Friendship', foreign_keys='Friendship.friend_id', backref='receiver', lazy='dynamic')
    
    def set_password(self, password):
        """Hash and set password"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check if provided password matches hash"""
        return check_password_hash(self.password_hash, password)
    
    def get_full_name(self):
        """Get user's full name"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.username
    
    def get_friends(self):
        """Get list of confirmed friends"""
        sent_friends = db.session.query(User).join(
            Friendship, User.id == Friendship.friend_id
        ).filter(
            Friendship.user_id == self.id,
            Friendship.status == 'accepted'
        )
        
        received_friends = db.session.query(User).join(
            Friendship, User.id == Friendship.user_id
        ).filter(
            Friendship.friend_id == self.id,
            Friendship.status == 'accepted'
        )
        
        return sent_friends.union(received_friends).all()
    
    def is_friend_with(self, user):
        """Check if users are friends"""
        return Friendship.query.filter(
            db.or_(
                db.and_(Friendship.user_id == self.id, Friendship.friend_id == user.id),
                db.and_(Friendship.user_id == user.id, Friendship.friend_id == self.id)
            ),
            Friendship.status == 'accepted'
        ).first() is not None
    
    def to_dict(self):
        """Convert user to dictionary"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'full_name': self.get_full_name(),
            'avatar_url': self.avatar_url,
            'bio': self.bio,
            'is_active': self.is_active,
            'is_verified': self.is_verified,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }
    
    def __repr__(self):
        return f'<User {self.username}>'


# Import here to avoid circular imports
from app.models.friendship import Friendship
