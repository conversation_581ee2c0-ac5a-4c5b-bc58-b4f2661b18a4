"""
Match model for storing game match data
"""
from datetime import datetime
from app import db


class Match(db.Model):
    """Model for individual game matches"""
    __tablename__ = 'matches'
    
    id = db.Column(db.Integer, primary_key=True)
    player_profile_id = db.Column(db.<PERSON>ger, db.<PERSON><PERSON>('player_profiles.id'), nullable=False)
    game_id = db.Column(db.Integer, db.ForeignKey('games.id'), nullable=False)
    game_mode_id = db.Column(db.Integer, db.Foreign<PERSON>ey('game_modes.id'))
    
    # Match identifiers
    match_id = db.Column(db.String(100), nullable=False)  # Game-specific match ID
    external_match_id = db.Column(db.String(100))         # External API match ID
    
    # Match details
    match_date = db.Column(db.DateTime, nullable=False)
    duration = db.Column(db.Integer)  # Match duration in seconds
    map_name = db.Column(db.String(100))
    
    # Match outcome
    result = db.Column(db.String(20))  # 'win', 'loss', 'draw'
    team_score = db.Column(db.Integer)
    enemy_score = db.Column(db.Integer)
    
    # Player performance
    kills = db.Column(db.Integer, default=0)
    deaths = db.Column(db.Integer, default=0)
    assists = db.Column(db.Integer, default=0)
    score = db.Column(db.Integer, default=0)
    
    # Game-specific stats (stored as JSON)
    game_specific_stats = db.Column(db.JSON)
    
    # Rank information (if ranked match)
    rank_before = db.Column(db.String(50))
    rank_after = db.Column(db.String(50))
    rating_before = db.Column(db.Integer)
    rating_after = db.Column(db.Integer)
    rating_change = db.Column(db.Integer)
    
    # Match metadata
    is_ranked = db.Column(db.Boolean, default=False)
    is_placement = db.Column(db.Boolean, default=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Unique constraint to prevent duplicate matches
    __table_args__ = (db.UniqueConstraint('player_profile_id', 'match_id', name='unique_player_match'),)
    
    @property
    def kd_ratio(self):
        """Calculate K/D ratio for this match"""
        if self.deaths == 0:
            return float(self.kills) if self.kills > 0 else 0.0
        return round(self.kills / self.deaths, 2)
    
    @property
    def kda_ratio(self):
        """Calculate KDA ratio for this match"""
        if self.deaths == 0:
            return float(self.kills + self.assists) if (self.kills + self.assists) > 0 else 0.0
        return round((self.kills + self.assists) / self.deaths, 2)
    
    def get_performance_rating(self):
        """Get a simple performance rating based on KDA and result"""
        base_score = self.kda_ratio * 10
        
        if self.result == 'win':
            base_score *= 1.2
        elif self.result == 'loss':
            base_score *= 0.8
        
        return round(base_score, 1)
    
    def get_duration_formatted(self):
        """Get formatted match duration"""
        if not self.duration:
            return "Unknown"
        
        minutes = self.duration // 60
        seconds = self.duration % 60
        return f"{minutes}:{seconds:02d}"
    
    def to_dict(self):
        """Convert match to dictionary"""
        return {
            'id': self.id,
            'match_id': self.match_id,
            'match_date': self.match_date.isoformat() if self.match_date else None,
            'duration': self.duration,
            'duration_formatted': self.get_duration_formatted(),
            'map_name': self.map_name,
            'result': self.result,
            'team_score': self.team_score,
            'enemy_score': self.enemy_score,
            'kills': self.kills,
            'deaths': self.deaths,
            'assists': self.assists,
            'score': self.score,
            'kd_ratio': self.kd_ratio,
            'kda_ratio': self.kda_ratio,
            'performance_rating': self.get_performance_rating(),
            'rank_before': self.rank_before,
            'rank_after': self.rank_after,
            'rating_change': self.rating_change,
            'is_ranked': self.is_ranked,
            'is_placement': self.is_placement,
            'game_specific_stats': self.game_specific_stats
        }
    
    @staticmethod
    def get_recent_matches(player_profile_id, limit=20):
        """Get recent matches for a player profile"""
        return Match.query.filter_by(player_profile_id=player_profile_id)\
                         .order_by(Match.match_date.desc())\
                         .limit(limit).all()
    
    @staticmethod
    def get_matches_by_date_range(player_profile_id, start_date, end_date):
        """Get matches within a date range"""
        return Match.query.filter(
            Match.player_profile_id == player_profile_id,
            Match.match_date >= start_date,
            Match.match_date <= end_date
        ).order_by(Match.match_date.desc()).all()
    
    def __repr__(self):
        return f'<Match {self.match_id}: {self.result} ({self.kills}/{self.deaths}/{self.assists})>'
