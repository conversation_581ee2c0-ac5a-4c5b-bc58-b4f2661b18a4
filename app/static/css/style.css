/* Custom styles for Game Stats Tracker */

:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
}

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Card Styles */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/* Dashboard Stats Cards */
.card.bg-primary,
.card.bg-success,
.card.bg-info,
.card.bg-warning,
.card.bg-danger {
    border: none;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

/* Profile Cards */
.profile-card {
    transition: transform 0.2s ease-in-out;
}

.profile-card:hover {
    transform: translateY(-2px);
}

/* Match Result Badges */
.badge.bg-success {
    background-color: var(--success-color) !important;
}

.badge.bg-danger {
    background-color: var(--danger-color) !important;
}

/* Achievement Cards */
.achievement-card {
    border: 2px solid transparent;
    transition: border-color 0.3s ease;
}

.achievement-card.rarity-common {
    border-color: #6c757d;
}

.achievement-card.rarity-rare {
    border-color: #007bff;
}

.achievement-card.rarity-epic {
    border-color: #6f42c1;
}

.achievement-card.rarity-legendary {
    border-color: #fd7e14;
}

/* Form Styles */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

/* Hero Section */
.jumbotron {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

/* Stats Charts */
.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .jumbotron {
        padding: 2rem 1rem;
    }
    
    .display-4 {
        font-size: 2rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Utility Classes */
.text-shadow {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.border-radius-lg {
    border-radius: 0.5rem;
}

.box-shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.box-shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Game-specific Colors */
.valorant-theme {
    background: linear-gradient(135deg, #ff4655 0%, #bd3944 100%);
}

.cs2-theme {
    background: linear-gradient(135deg, #f79100 0%, #d67700 100%);
}

.bgmi-theme {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
}
