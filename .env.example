# Flask Configuration
SECRET_KEY=your-secret-key-here
FLASK_ENV=development

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost/game_stats_tracker
DEV_DATABASE_URL=postgresql://username:password@localhost/game_stats_tracker_dev
TEST_DATABASE_URL=postgresql://username:password@localhost/game_stats_tracker_test

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Game API Keys
# Get your Riot API key from: https://developer.riotgames.com/
RIOT_API_KEY=your-riot-api-key-here

# Get your Steam API key from: https://steamcommunity.com/dev/apikey
STEAM_API_KEY=your-steam-api-key-here

# OAuth Configuration (optional)
STEAM_OAUTH_CLIENT_ID=your-steam-oauth-client-id
STEAM_OAUTH_CLIENT_SECRET=your-steam-oauth-client-secret

# Email Configuration
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Game API Keys
RIOT_API_KEY=your-riot-api-key
STEAM_API_KEY=your-steam-api-key

# OAuth Configuration
STEAM_OAUTH_CLIENT_ID=your-steam-client-id
STEAM_OAUTH_CLIENT_SECRET=your-steam-client-secret

RIOT_OAUTH_CLIENT_ID=your-riot-client-id
RIOT_OAUTH_CLIENT_SECRET=your-riot-client-secret
